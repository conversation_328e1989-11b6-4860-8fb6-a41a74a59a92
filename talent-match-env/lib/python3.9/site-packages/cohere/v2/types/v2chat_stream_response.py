# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from ...types.chat_content_delta_event_delta import Chat<PERSON>ontentDeltaEventDelta
from ...types.chat_content_start_event_delta import ChatContentStartEventDelta
from ...types.chat_message_end_event_delta import ChatMessageEndEventDelta
from ...types.chat_message_start_event_delta import ChatMessageStartEventDelta
from ...types.chat_tool_call_delta_event_delta import ChatToolCallDeltaEventDelta
from ...types.chat_tool_call_start_event_delta import ChatToolCallStartEventDelta
from ...types.chat_tool_plan_delta_event_delta import ChatToolPlanDeltaEventDelta
from ...types.citation_start_event_delta import CitationStartEventDelta
from ...types.logprob_item import LogprobItem


class MessageStartV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["message-start"] = "message-start"
    id: typing.Optional[str] = None
    delta: typing.Optional[ChatMessageStartEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ContentStartV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["content-start"] = "content-start"
    index: typing.Optional[int] = None
    delta: typing.Optional[ChatContentStartEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ContentDeltaV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["content-delta"] = "content-delta"
    index: typing.Optional[int] = None
    delta: typing.Optional[ChatContentDeltaEventDelta] = None
    logprobs: typing.Optional[LogprobItem] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ContentEndV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["content-end"] = "content-end"
    index: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ToolPlanDeltaV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["tool-plan-delta"] = "tool-plan-delta"
    delta: typing.Optional[ChatToolPlanDeltaEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ToolCallStartV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["tool-call-start"] = "tool-call-start"
    index: typing.Optional[int] = None
    delta: typing.Optional[ChatToolCallStartEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ToolCallDeltaV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["tool-call-delta"] = "tool-call-delta"
    index: typing.Optional[int] = None
    delta: typing.Optional[ChatToolCallDeltaEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ToolCallEndV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["tool-call-end"] = "tool-call-end"
    index: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class CitationStartV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["citation-start"] = "citation-start"
    index: typing.Optional[int] = None
    delta: typing.Optional[CitationStartEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class CitationEndV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["citation-end"] = "citation-end"
    index: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class MessageEndV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["message-end"] = "message-end"
    id: typing.Optional[str] = None
    delta: typing.Optional[ChatMessageEndEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class DebugV2ChatStreamResponse(UncheckedBaseModel):
    """
    StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
    """

    type: typing.Literal["debug"] = "debug"
    prompt: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


V2ChatStreamResponse = typing_extensions.Annotated[
    typing.Union[
        MessageStartV2ChatStreamResponse,
        ContentStartV2ChatStreamResponse,
        ContentDeltaV2ChatStreamResponse,
        ContentEndV2ChatStreamResponse,
        ToolPlanDeltaV2ChatStreamResponse,
        ToolCallStartV2ChatStreamResponse,
        ToolCallDeltaV2ChatStreamResponse,
        ToolCallEndV2ChatStreamResponse,
        CitationStartV2ChatStreamResponse,
        CitationEndV2ChatStreamResponse,
        MessageEndV2ChatStreamResponse,
        DebugV2ChatStreamResponse,
    ],
    UnionMetadata(discriminant="type"),
]
