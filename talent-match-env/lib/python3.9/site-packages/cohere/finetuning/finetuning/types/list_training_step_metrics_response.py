# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel
from .training_step_metrics import TrainingStepMetrics


class ListTrainingStepMetricsResponse(UncheckedBaseModel):
    """
    Response to a request to list training-step metrics of a fine-tuned model.
    """

    step_metrics: typing.Optional[typing.List[TrainingStepMetrics]] = pydantic.Field(default=None)
    """
    The metrics for each step the evaluation was run on.
    """

    next_page_token: typing.Optional[str] = pydantic.Field(default=None)
    """
    Pagination token to retrieve the next page of results. If the value is "",
    it means no further results for the request.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
