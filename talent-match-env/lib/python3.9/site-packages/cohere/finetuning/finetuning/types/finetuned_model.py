# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

import pydantic
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel
from .settings import Settings
from .status import Status


class FinetunedModel(UncheckedBaseModel):
    """
    This resource represents a fine-tuned model.
    """

    id: typing.Optional[str] = pydantic.Field(default=None)
    """
    read-only. FinetunedModel ID.
    """

    name: str = pydantic.Field()
    """
    FinetunedModel name (e.g. `foobar`).
    """

    creator_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    read-only. User ID of the creator.
    """

    organization_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    read-only. Organization ID.
    """

    settings: Settings = pydantic.Field()
    """
    FinetunedModel settings such as dataset, hyperparameters...
    """

    status: typing.Optional[Status] = pydantic.Field(default=None)
    """
    read-only. Current stage in the life-cycle of the fine-tuned model.
    """

    created_at: typing.Optional[dt.datetime] = pydantic.Field(default=None)
    """
    read-only. Creation timestamp.
    """

    updated_at: typing.Optional[dt.datetime] = pydantic.Field(default=None)
    """
    read-only. Latest update timestamp.
    """

    completed_at: typing.Optional[dt.datetime] = pydantic.Field(default=None)
    """
    read-only. Timestamp for the completed fine-tuning.
    """

    last_used: typing.Optional[dt.datetime] = pydantic.Field(default=None)
    """
    read-only. Deprecated: Timestamp for the latest request to this fine-tuned model.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
