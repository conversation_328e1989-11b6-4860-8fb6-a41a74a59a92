# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

import pydantic
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel


class TrainingStepMetrics(UncheckedBaseModel):
    """
    The evaluation metrics at a given step of the training of a fine-tuned model.
    """

    created_at: typing.Optional[dt.datetime] = pydantic.Field(default=None)
    """
    Creation timestamp.
    """

    step_number: typing.Optional[int] = pydantic.Field(default=None)
    """
    Step number.
    """

    metrics: typing.Optional[typing.Dict[str, float]] = pydantic.Field(default=None)
    """
    Map of names and values for each evaluation metrics.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
