# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from .finetuning.types.create_finetuned_model_response import CreateFinetunedModelResponse
from .finetuning.types.delete_finetuned_model_response import DeleteFinetunedModelResponse
from .finetuning.types.finetuned_model import FinetunedModel
from .finetuning.types.get_finetuned_model_response import GetFinetunedModelResponse
from .finetuning.types.list_events_response import ListEventsResponse
from .finetuning.types.list_finetuned_models_response import ListFinetunedModelsResponse
from .finetuning.types.list_training_step_metrics_response import ListTrainingStepMetricsResponse
from .finetuning.types.settings import Settings
from .finetuning.types.status import Status
from .finetuning.types.update_finetuned_model_response import UpdateFinetunedModelResponse
from .raw_client import AsyncRawF<PERSON>uningClient, RawFinetuningClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class FinetuningClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawFinetuningClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawFinetuningClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawFinetuningClient
        """
        return self._raw_client

    def list_finetuned_models(
        self,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        order_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListFinetunedModelsResponse:
        """
        Parameters
        ----------
        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        order_by : typing.Optional[str]
            Comma separated list of fields. For example: "created_at,name". The default
            sorting order is ascending. To specify descending order for a field, append
            " desc" to the field name. For example: "created_at desc,name".

            Supported sorting fields:
              - created_at (default)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListFinetunedModelsResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.list_finetuned_models()
        """
        _response = self._raw_client.list_finetuned_models(
            page_size=page_size, page_token=page_token, order_by=order_by, request_options=request_options
        )
        return _response.data

    def create_finetuned_model(
        self, *, request: FinetunedModel, request_options: typing.Optional[RequestOptions] = None
    ) -> CreateFinetunedModelResponse:
        """
        Parameters
        ----------
        request : FinetunedModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateFinetunedModelResponse
            A successful response.

        Examples
        --------
        from cohere import Client
        from cohere.finetuning.finetuning import BaseModel, FinetunedModel, Settings

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.create_finetuned_model(
            request=FinetunedModel(
                name="api-test",
                settings=Settings(
                    base_model=BaseModel(
                        base_type="BASE_TYPE_CHAT",
                    ),
                    dataset_id="my-dataset-id",
                ),
            ),
        )
        """
        _response = self._raw_client.create_finetuned_model(request=request, request_options=request_options)
        return _response.data

    def get_finetuned_model(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            The fine-tuned model ID.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetFinetunedModelResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.get_finetuned_model(
            id="id",
        )
        """
        _response = self._raw_client.get_finetuned_model(id, request_options=request_options)
        return _response.data

    def delete_finetuned_model(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            The fine-tuned model ID.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteFinetunedModelResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.delete_finetuned_model(
            id="id",
        )
        """
        _response = self._raw_client.delete_finetuned_model(id, request_options=request_options)
        return _response.data

    def update_finetuned_model(
        self,
        id: str,
        *,
        name: str,
        settings: Settings,
        status: typing.Optional[Status] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            FinetunedModel ID.

        name : str
            FinetunedModel name (e.g. `foobar`).

        settings : Settings
            FinetunedModel settings such as dataset, hyperparameters...

        status : typing.Optional[Status]
            Current stage in the life-cycle of the fine-tuned model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateFinetunedModelResponse
            A successful response.

        Examples
        --------
        from cohere import Client
        from cohere.finetuning.finetuning import BaseModel, Settings

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.update_finetuned_model(
            id="id",
            name="name",
            settings=Settings(
                base_model=BaseModel(
                    base_type="BASE_TYPE_UNSPECIFIED",
                ),
                dataset_id="dataset_id",
            ),
        )
        """
        _response = self._raw_client.update_finetuned_model(
            id, name=name, settings=settings, status=status, request_options=request_options
        )
        return _response.data

    def list_events(
        self,
        finetuned_model_id: str,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        order_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListEventsResponse:
        """
        Parameters
        ----------
        finetuned_model_id : str
            The parent fine-tuned model ID.

        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        order_by : typing.Optional[str]
            Comma separated list of fields. For example: "created_at,name". The default
            sorting order is ascending. To specify descending order for a field, append
            " desc" to the field name. For example: "created_at desc,name".

            Supported sorting fields:
              - created_at (default)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListEventsResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.list_events(
            finetuned_model_id="finetuned_model_id",
        )
        """
        _response = self._raw_client.list_events(
            finetuned_model_id,
            page_size=page_size,
            page_token=page_token,
            order_by=order_by,
            request_options=request_options,
        )
        return _response.data

    def list_training_step_metrics(
        self,
        finetuned_model_id: str,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListTrainingStepMetricsResponse:
        """
        Parameters
        ----------
        finetuned_model_id : str
            The parent fine-tuned model ID.

        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListTrainingStepMetricsResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.finetuning.list_training_step_metrics(
            finetuned_model_id="finetuned_model_id",
        )
        """
        _response = self._raw_client.list_training_step_metrics(
            finetuned_model_id, page_size=page_size, page_token=page_token, request_options=request_options
        )
        return _response.data


class AsyncFinetuningClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawFinetuningClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawFinetuningClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawFinetuningClient
        """
        return self._raw_client

    async def list_finetuned_models(
        self,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        order_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListFinetunedModelsResponse:
        """
        Parameters
        ----------
        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        order_by : typing.Optional[str]
            Comma separated list of fields. For example: "created_at,name". The default
            sorting order is ascending. To specify descending order for a field, append
            " desc" to the field name. For example: "created_at desc,name".

            Supported sorting fields:
              - created_at (default)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListFinetunedModelsResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.list_finetuned_models()


        asyncio.run(main())
        """
        _response = await self._raw_client.list_finetuned_models(
            page_size=page_size, page_token=page_token, order_by=order_by, request_options=request_options
        )
        return _response.data

    async def create_finetuned_model(
        self, *, request: FinetunedModel, request_options: typing.Optional[RequestOptions] = None
    ) -> CreateFinetunedModelResponse:
        """
        Parameters
        ----------
        request : FinetunedModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateFinetunedModelResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient
        from cohere.finetuning.finetuning import BaseModel, FinetunedModel, Settings

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.create_finetuned_model(
                request=FinetunedModel(
                    name="api-test",
                    settings=Settings(
                        base_model=BaseModel(
                            base_type="BASE_TYPE_CHAT",
                        ),
                        dataset_id="my-dataset-id",
                    ),
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_finetuned_model(request=request, request_options=request_options)
        return _response.data

    async def get_finetuned_model(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            The fine-tuned model ID.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetFinetunedModelResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.get_finetuned_model(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_finetuned_model(id, request_options=request_options)
        return _response.data

    async def delete_finetuned_model(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            The fine-tuned model ID.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteFinetunedModelResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.delete_finetuned_model(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete_finetuned_model(id, request_options=request_options)
        return _response.data

    async def update_finetuned_model(
        self,
        id: str,
        *,
        name: str,
        settings: Settings,
        status: typing.Optional[Status] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateFinetunedModelResponse:
        """
        Parameters
        ----------
        id : str
            FinetunedModel ID.

        name : str
            FinetunedModel name (e.g. `foobar`).

        settings : Settings
            FinetunedModel settings such as dataset, hyperparameters...

        status : typing.Optional[Status]
            Current stage in the life-cycle of the fine-tuned model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateFinetunedModelResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient
        from cohere.finetuning.finetuning import BaseModel, Settings

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.update_finetuned_model(
                id="id",
                name="name",
                settings=Settings(
                    base_model=BaseModel(
                        base_type="BASE_TYPE_UNSPECIFIED",
                    ),
                    dataset_id="dataset_id",
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update_finetuned_model(
            id, name=name, settings=settings, status=status, request_options=request_options
        )
        return _response.data

    async def list_events(
        self,
        finetuned_model_id: str,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        order_by: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListEventsResponse:
        """
        Parameters
        ----------
        finetuned_model_id : str
            The parent fine-tuned model ID.

        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        order_by : typing.Optional[str]
            Comma separated list of fields. For example: "created_at,name". The default
            sorting order is ascending. To specify descending order for a field, append
            " desc" to the field name. For example: "created_at desc,name".

            Supported sorting fields:
              - created_at (default)

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListEventsResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.list_events(
                finetuned_model_id="finetuned_model_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list_events(
            finetuned_model_id,
            page_size=page_size,
            page_token=page_token,
            order_by=order_by,
            request_options=request_options,
        )
        return _response.data

    async def list_training_step_metrics(
        self,
        finetuned_model_id: str,
        *,
        page_size: typing.Optional[int] = None,
        page_token: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListTrainingStepMetricsResponse:
        """
        Parameters
        ----------
        finetuned_model_id : str
            The parent fine-tuned model ID.

        page_size : typing.Optional[int]
            Maximum number of results to be returned by the server. If 0, defaults to
            50.

        page_token : typing.Optional[str]
            Request a specific page of the list results.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListTrainingStepMetricsResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.finetuning.list_training_step_metrics(
                finetuned_model_id="finetuned_model_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list_training_step_metrics(
            finetuned_model_id, page_size=page_size, page_token=page_token, request_options=request_options
        )
        return _response.data
