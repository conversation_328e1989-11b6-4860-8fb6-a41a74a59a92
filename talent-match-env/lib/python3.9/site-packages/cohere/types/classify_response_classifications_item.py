# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .classify_response_classifications_item_classification_type import (
    ClassifyResponseClassificationsItemClassificationType,
)
from .classify_response_classifications_item_labels_value import ClassifyResponseClassificationsItemLabelsValue


class ClassifyResponseClassificationsItem(UncheckedBaseModel):
    id: str
    input: typing.Optional[str] = pydantic.Field(default=None)
    """
    The input text that was classified
    """

    prediction: typing.Optional[str] = pydantic.Field(default=None)
    """
    The predicted label for the associated query (only filled for single-label models)
    """

    predictions: typing.List[str] = pydantic.Field()
    """
    An array containing the predicted labels for the associated query (only filled for single-label classification)
    """

    confidence: typing.Optional[float] = pydantic.Field(default=None)
    """
    The confidence score for the top predicted class (only filled for single-label classification)
    """

    confidences: typing.List[float] = pydantic.Field()
    """
    An array containing the confidence scores of all the predictions in the same order
    """

    labels: typing.Dict[str, ClassifyResponseClassificationsItemLabelsValue] = pydantic.Field()
    """
    A map containing each label and its confidence score according to the classifier. All the confidence scores add up to 1 for single-label classification. For multi-label classification the label confidences are independent of each other, so they don't have to sum up to 1.
    """

    classification_type: ClassifyResponseClassificationsItemClassificationType = pydantic.Field()
    """
    The type of classification performed
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
