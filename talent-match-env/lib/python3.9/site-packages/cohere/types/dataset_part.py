# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class DatasetPart(UncheckedBaseModel):
    id: str = pydantic.Field()
    """
    The dataset part ID
    """

    name: str = pydantic.Field()
    """
    The name of the dataset part
    """

    url: typing.Optional[str] = pydantic.Field(default=None)
    """
    The download url of the file
    """

    index: typing.Optional[int] = pydantic.Field(default=None)
    """
    The index of the file
    """

    size_bytes: typing.Optional[int] = pydantic.Field(default=None)
    """
    The size of the file in bytes
    """

    num_rows: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of rows in the file
    """

    original_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    The download url of the original file
    """

    samples: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    The first few rows of the parsed file
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
