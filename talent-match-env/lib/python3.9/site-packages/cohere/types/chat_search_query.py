# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ChatSearchQuery(UncheckedBaseModel):
    """
    The generated search query. Contains the text of the query and a unique identifier for the query.
    """

    text: str = pydantic.Field()
    """
    The text of the search query.
    """

    generation_id: str = pydantic.Field()
    """
    Unique identifier for the generated search query. Useful for submitting feedback.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
