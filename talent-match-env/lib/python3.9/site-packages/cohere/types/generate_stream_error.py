# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from .finish_reason import FinishReason
from .generate_stream_event import GenerateStreamEvent


class GenerateStreamError(GenerateStreamEvent):
    index: typing.Optional[int] = pydantic.Field(default=None)
    """
    Refers to the nth generation. Only present when `num_generations` is greater than zero.
    """

    is_finished: bool
    finish_reason: FinishReason
    err: str = pydantic.Field()
    """
    Error message
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
