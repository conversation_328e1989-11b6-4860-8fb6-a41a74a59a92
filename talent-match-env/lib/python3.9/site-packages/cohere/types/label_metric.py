# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class LabelMetric(UncheckedBaseModel):
    total_examples: typing.Optional[int] = pydantic.Field(default=None)
    """
    Total number of examples for this label
    """

    label: typing.Optional[str] = pydantic.Field(default=None)
    """
    value of the label
    """

    samples: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    samples for this label
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
