# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .user_message_v2content import UserMessageV2Content


class UserMessageV2(UncheckedBaseModel):
    """
    A message from the user.
    """

    content: UserMessageV2Content = pydantic.Field()
    """
    The content of the message. This can be a string or a list of content blocks.
    If a string is provided, it will be treated as a text content block.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
