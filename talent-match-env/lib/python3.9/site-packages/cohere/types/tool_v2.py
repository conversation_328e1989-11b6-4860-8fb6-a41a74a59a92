# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .tool_v2function import ToolV2Function


class ToolV2(UncheckedBaseModel):
    type: typing.Optional[typing.Literal["function"]] = None
    function: typing.Optional[ToolV2Function] = pydantic.Field(default=None)
    """
    The function to be executed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
