# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class Document(UncheckedBaseModel):
    """
    Relevant information that could be used by the model to generate a more accurate reply.
    The content of each document are generally short (should be under 300 words). Metadata should be used to provide additional information, both the key name and the value will be
    passed to the model.
    """

    data: typing.Dict[str, typing.Optional[typing.Any]] = pydantic.Field()
    """
    A relevant document that the model can cite to generate a more accurate reply. Each document is a string-any dictionary.
    """

    id: typing.Optional[str] = pydantic.Field(default=None)
    """
    Unique identifier for this document which will be referenced in citations. If not provided an ID will be automatically generated.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
