# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .api_meta import ApiMeta
from .single_generation import SingleGeneration


class Generation(UncheckedBaseModel):
    id: str
    prompt: typing.Optional[str] = pydantic.Field(default=None)
    """
    Prompt used for generations.
    """

    generations: typing.List[SingleGeneration] = pydantic.Field()
    """
    List of generated results
    """

    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
