# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .embed_image_url import EmbedImageUrl


class EmbedImage(UncheckedBaseModel):
    """
    Image content of the input. Supported with Embed v3.0 and newer models.
    """

    image_url: typing.Optional[EmbedImageUrl] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
