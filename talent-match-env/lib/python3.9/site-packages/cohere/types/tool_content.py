# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .document import Document


class TextToolContent(UncheckedBaseModel):
    """
    A content block which contains information about the content of a tool result
    """

    type: typing.Literal["text"] = "text"
    text: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class DocumentToolContent(UncheckedBaseModel):
    """
    A content block which contains information about the content of a tool result
    """

    type: typing.Literal["document"] = "document"
    document: Document

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


ToolContent = typing_extensions.Annotated[
    typing.Union[TextToolContent, DocumentToolContent], UnionMetadata(discriminant="type")
]
