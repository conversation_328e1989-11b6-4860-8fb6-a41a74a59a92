# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from .chat_stream_end_event_finish_reason import ChatStreamEndEventFinishReason
from .chat_stream_event import ChatStreamEvent
from .non_streamed_chat_response import NonStreamedChatResponse


class ChatStreamEndEvent(ChatStreamEvent):
    finish_reason: ChatStreamEndEventFinishReason = pydantic.Field()
    """
    - `COMPLETE` - the model sent back a finished reply
    - `ERROR_LIMIT` - the reply was cut off because the model reached the maximum number of tokens for its context length
    - `MAX_TOKENS` - the reply was cut off because the model reached the maximum number of tokens specified by the max_tokens parameter
    - `ERROR` - something went wrong when generating the reply
    - `ERROR_TOXIC` - the model generated a reply that was deemed toxic
    """

    response: NonStreamedChatResponse = pydantic.Field()
    """
    The consolidated response from the model. Contains the generated reply and all the other information streamed back in the previous events.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
