# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .assistant_message_v2content import AssistantMessageV2Content
from .citation import Citation
from .system_message_v2content import SystemMessageV2Content
from .tool_call_v2 import ToolCallV2
from .tool_message_v2content import ToolMessageV2Content
from .user_message_v2content import UserMessageV2Content


class UserChatMessageV2(UncheckedBaseModel):
    """
    Represents a single message in the chat history from a given role.
    """

    role: typing.Literal["user"] = "user"
    content: UserMessageV2Content

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class AssistantChatMessageV2(UncheckedBaseModel):
    """
    Represents a single message in the chat history from a given role.
    """

    role: typing.Literal["assistant"] = "assistant"
    tool_calls: typing.Optional[typing.List[ToolCallV2]] = None
    tool_plan: typing.Optional[str] = None
    content: typing.Optional[AssistantMessageV2Content] = None
    citations: typing.Optional[typing.List[Citation]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class SystemChatMessageV2(UncheckedBaseModel):
    """
    Represents a single message in the chat history from a given role.
    """

    role: typing.Literal["system"] = "system"
    content: SystemMessageV2Content

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ToolChatMessageV2(UncheckedBaseModel):
    """
    Represents a single message in the chat history from a given role.
    """

    role: typing.Literal["tool"] = "tool"
    tool_call_id: str
    content: ToolMessageV2Content

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


ChatMessageV2 = typing_extensions.Annotated[
    typing.Union[UserChatMessageV2, AssistantChatMessageV2, SystemChatMessageV2, ToolChatMessageV2],
    UnionMetadata(discriminant="role"),
]
