Here is the PRD for the Talent Matching Agentic RAG project.

```prd.txt
# Product Requirements Document: MVP for "Talent Match" Agentic RAG

**Version:** 1.0
**Date:** 2025-07-23
**Author:** Gemini AI Assistant

## 1. Vision & Mission

**Vision:** To revolutionize talent acquisition by moving beyond keyword-based resume searching to a deep, contextual understanding of candidate skills and experience.

**Mission (MVP):** To build an intelligent agent that assists recruiters by accurately matching candidates to job descriptions using a state-of-the-art Retrieval-Augmented Generation (RAG) system. The agent will leverage a knowledge graph to understand the relationships between skills, roles, and experiences, providing explainable and high-quality matching recommendations.

---

## 2. Target Audience

For the MVP, the primary target user is a **Technical Recruiter** at a mid-to-large-sized technology company.

**User Pains:**
*   Manually sifting through hundreds of resumes is time-consuming and inefficient.
*   Simple keyword searches miss qualified candidates who use different terminology (e.g., "AWS" vs. "Amazon Web Services," "team lead" vs. "engineering manager").
*   It's difficult to quickly gauge the depth of a candidate's experience in a specific required skill.
*   Presenting a shortlist of candidates to a hiring manager requires manually summarizing each profile's strengths.

---

## 3. Core Features & User Stories (MVP)

The MVP will focus on a core matching workflow within a simple, interactive interface.

*   **Story 1: Data Ingestion**
    *   **As a recruiter,** I want to upload a directory of candidate resumes (in PDF format) so the system can process and understand them.
    *   **Acceptance Criteria:** The system uses LightRAG to process PDFs, chunk the text, and build out a knowledge base. LightRAG's ability to handle multiple file types is a key advantage. [4]

*   **Story 2: Defining the Target Role**
    *   **As a recruiter,** I want to paste the full text of a job description (JD) into the application to set the context for my search.
    *   **Acceptance Criteria:** The JD text is used as the primary input for the matching query.

*   **Story 3: Natural Language Querying**
    *   **As a recruiter,** I want to ask a natural language question to find the best candidates for the provided JD.
    *   *Example Query:* "Find me the top 3 candidates for this Senior Python Developer role. I'm looking for strong experience with Django, AWS, and at least one project leading a small team."
    *   **Acceptance Criteria:** The system uses an agentic workflow to parse the question, retrieve relevant information using LightRAG, and generate a comprehensive answer.

*   **Story 4: Receiving Intelligent Matches**
    *   **As a recruiter,** I want to receive a clear, ranked list of matching candidates.
    *   **Acceptance Criteria:** The UI displays the top candidates. For each candidate, a concise, AI-generated summary explains *why* they are a good match, citing specific experiences from their resume. This leverages LightRAG's citation capabilities. [4]

*   **Story 5: Visualizing Candidate Knowledge**
    *   **As a recruiter,** I want to be able to click on a candidate's name and see a visualization of their knowledge graph.
    *   **Acceptance Criteria:** A graph visualization (similar to `examples/graph_visual_with_html.py`) is displayed, showing the connections between the candidate's skills, past companies, and project roles as extracted by LightRAG. This provides immediate, deep insight.

---

## 4. Technical Architecture

The architecture is designed to be powerful and flexible, using modern, open-source tools as specified. It will be modeled after the `light-rag-agent` and various `examples` provided in the context.

*   **Frontend:**
    *   **Framework:** Streamlit
    *   **Rationale:** Provides a fast and easy way to build an interactive web application with Python. The `light-rag-agent/LightRAG/streamlit_app.py` serves as a direct template for the chat interface.

*   **Backend / Agentic Layer:**
    *   **Frameworks:** Python, LangGraph, LightRAG. [5, 12]
    *   **Orchestration:** LangGraph will manage the state and flow of the agent. [21] It will define the steps: receiving a user query, deciding to use the retrieval tool, calling the tool, and passing the results to the LLM for generation.
    *   **RAG Engine:** LightRAG will be the core engine for knowledge processing and retrieval. Its hybrid retrieval mode, combining vector search and knowledge graph traversal, is essential for deep contextual matching. [1, 14, 17]
        *   **Ingestion:** `rag.insert()` will process resumes.
        *   **Retrieval:** The LangGraph agent will call a `retrieve` tool, which in turn uses `rag.aquery(mode="hybrid")`. [11, 19]

*   **AI Model:**
    *   **Model:** Google Gemini 1.5 Flash
    *   **Integration:** We will implement a custom `llm_model_func` for LightRAG to call the Gemini API. The implementation will follow the pattern shown in `examples/lightrag_gemini_demo.py`.

*   **Data Storage:**
    *   **Vector Database:** ChromaDB. [23]
    *   **Knowledge Graph Database:** Neo4j. [6, 15]
    *   **Integration:** LightRAG will be configured to use these external databases instead of its default file-based storage. This enhances scalability and query power. [18] The configuration will be similar to `examples/unofficial-sample/lightrag_openai_neo4j_milvus_redis_demo.py`, but adapted for ChromaDB.
        ```python
        # In rag initialization
        rag = LightRAG(
            # ... other configs
            vector_storage="ChromaVectorDBStorage", # Assumes/Requires a ChromaDB storage adapter for LightRAG
            graph_storage="Neo4JStorage",
            # ...
        )
        ```
    *   **Rationale:** Neo4j is explicitly supported by LightRAG for graph storage. [4, 8, 11] ChromaDB is a lightweight and popular choice for local and production vector storage, fitting the MVP's scope. [11, 23]

*   **Setup & Orchestration:**
    *   **Tool:** Taskfile (`taskfile.dev`)
    *   **Rationale:** As requested, Taskfile will be used as a simpler, more modern alternative to Docker Compose for running local development services. [2, 7] It will manage starting the Neo4j and ChromaDB containers and running the application.

---

## 5. Data Flow

1.  **Ingestion (Offline Process):**
    *   A recruiter places candidate PDF resumes into a designated input folder.
    *   An ingestion script (`ingest.py`) is run via `task ingest`.
    *   The script iterates through the PDFs, extracts the text, and calls `rag.insert(resume_text)`.
    *   **LightRAG Pipeline:**
        1.  Chunks the document.
        2.  Generates embeddings for each chunk (using a Gemini embedding model or another specified model) and stores them in **ChromaDB**. [15]
        3.  Extracts entities (Skills, Companies, Dates, etc.) and relationships. [15]
        4.  Upserts these entities and relationships as nodes and edges into **Neo4j**. [15]

2.  **Querying (Real-time Process):**
    *   The recruiter opens the Streamlit application.
    *   They paste a JD and ask a natural language question.
    *   The Streamlit app sends the request to the LangGraph agent.
    *   **LangGraph Flow:**
        1.  **`retrieval` Node:** The agent's `retrieve` tool is invoked. This tool calls `rag.aquery(question, param=QueryParam(mode="hybrid"))`.
        2.  **LightRAG Hybrid Query:** LightRAG performs a dual-level retrieval:
            *   It queries **ChromaDB** for semantically similar text chunks.
            *   It queries **Neo4j** to find related entities and traverse the knowledge graph for deeper context. [5, 10]
            *   (Optional but recommended) A reranker can be used to improve the quality of retrieved results. [4]
        3.  **`generation` Node:** The retrieved context (candidate chunks and graph data) is formatted and passed to the Gemini 1.5 Flash model with a system prompt asking it to rank the candidates and explain its reasoning.
        4.  **Output:** The final formatted text is streamed back to the Streamlit UI.

---

## 6. MVP Scope

**IN SCOPE:**
*   Ingestion of up to 1,000 PDF resumes.
*   Single-turn Question & Answer interface.
*   Matching based on a combination of a pasted JD and a natural language query.
*   Displaying a ranked list of candidates with explanations.
*   Static visualization of a selected candidate's knowledge graph.
*   Local deployment managed via Taskfile.

**OUT OF SCOPE:**
*   User accounts and multi-tenancy.
*   Real-time monitoring of a folder for new resumes.
*   Conversational, multi-turn chat with memory.
*   Direct integration with Applicant Tracking Systems (ATS).
*   Automated candidate outreach.
*   Fine-tuning the LLM.

---

## 7. Setup & Execution (`Taskfile.yml`)

A `Taskfile.yml` will be provided at the project root to manage dependencies and services, replacing the need for `docker-compose.yml`. [2, 16]

```yaml
# Taskfile.yml
version: '3'

vars:
  NEO4J_CONTAINER_NAME: talent-match-neo4j
  CHROMA_CONTAINER_NAME: talent-match-chroma

tasks:
  default:
    cmds:
      - task: services:up
      - task: run
    desc: "Starts all services and runs the application."

  setup:
    desc: "Install Python dependencies from requirements.txt"
    cmds:
      - pip install -r requirements.txt

  services:up:
    desc: "Start Neo4j and ChromaDB services using Docker."
    cmds:
      - |
        if [ ! \"$(docker ps -q -f name={{.NEO4J_CONTAINER_NAME}})\" ]; then
          if [ \"$(docker ps -aq -f status=exited -f name={{.NEO4J_CONTAINER_NAME}})\" ]; then
            docker start {{.NEO4J_CONTAINER_NAME}}
          else
            echo "Starting Neo4j..."
            docker run -d --name {{.NEO4J_CONTAINER_NAME}} -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/password neo4j:latest
          fi
        else
          echo "Neo4j is already running."
        fi
      - |
        if [ ! \"$(docker ps -q -f name={{.CHROMA_CONTAINER_NAME}})\" ]; then
          if [ \"$(docker ps -aq -f status=exited -f name={{.CHROMA_CONTAINER_NAME}})\" ]; then
            docker start {{.CHROMA_CONTAINER_NAME}}
          else
            echo "Starting ChromaDB..."
            docker run -d --name {{.CHROMA_CONTAINER_NAME}} -p 8000:8000 chromadb/chroma
          fi
        else
          echo "ChromaDB is already running."
        fi

  services:down:
    desc: "Stop and remove Neo4j and ChromaDB containers."
    cmds:
      - docker stop {{.NEO4J_CONTAINER_NAME}} && docker rm {{.NEO4J_CONTAINER_NAME}}
      - docker stop {{.CHROMA_CONTAINER_NAME}} && docker rm {{.CHROMA_CONTAINER_NAME}}

  ingest:
    desc: "Run the data ingestion script to process resumes."
    deps: [setup, services:up]
    cmds:
      - python ingest_data.py --path ./resumes
    
  run:
    desc: "Run the Streamlit application."
    deps: [setup, services:up]
    cmds:
      - streamlit run streamlit_app.py
```
```