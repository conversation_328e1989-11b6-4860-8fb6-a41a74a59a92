"""RAG Agent implementation using LangGraph, LightRAG, and Google Gemini."""

import os
import sys
import argparse
import asyncio
from dataclasses import dataclass
from typing import Optional, Dict, Any, List
import json

import google.generativeai as genai
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent
from lightrag import LightRAG, QueryParam
from lightrag.base import BaseKVStorage
from lightrag.utils import EmbeddingFunc, logger

from config import GEMINI_CONFIG, NEO4J_CONFIG, CHROMA_CONFIG, APP_CONFIG


# Configure Gemini API
genai.configure(api_key=GEMINI_CONFIG.api_key)


async def gemini_llm_func(
    prompt: str,
    system_prompt: str = None,
    history_messages: List[Dict] = None,
    **kwargs
) -> str:
    """Custom LLM function for LightRAG using Google Gemini."""
    try:
        model = genai.GenerativeModel(GEMINI_CONFIG.model_name)
        
        # Prepare the full prompt
        full_prompt = ""
        if system_prompt:
            full_prompt += f"System: {system_prompt}\n\n"
        
        if history_messages:
            for msg in history_messages:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                full_prompt += f"{role.capitalize()}: {content}\n"
        
        full_prompt += f"User: {prompt}\n\nAssistant:"
        
        # Generate response
        response = await model.generate_content_async(
            full_prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=GEMINI_CONFIG.temperature,
                max_output_tokens=GEMINI_CONFIG.max_tokens,
            )
        )
        
        return response.text
        
    except Exception as e:
        logger.error(f"Error in Gemini LLM function: {e}")
        return f"Error generating response: {str(e)}"


async def gemini_embedding_func(texts: List[str]) -> List[List[float]]:
    """Custom embedding function for LightRAG using Google Gemini."""
    try:
        embeddings = []
        
        for text in texts:
            result = genai.embed_content(
                model=GEMINI_CONFIG.embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            embeddings.append(result['embedding'])
        
        return embeddings
        
    except Exception as e:
        logger.error(f"Error in Gemini embedding function: {e}")
        # Return zero embeddings as fallback
        return [[0.0] * 768 for _ in texts]


@dataclass
class RAGDeps:
    """Dependencies for the RAG agent."""
    lightrag: LightRAG
    working_dir: str


class TalentMatchRAG:
    """Talent matching RAG system using LightRAG and Gemini."""
    
    def __init__(self, working_dir: str = None):
        self.working_dir = working_dir or APP_CONFIG.working_dir
        self.rag = None
        
    async def initialize(self):
        """Initialize the LightRAG system."""
        if not os.path.exists(self.working_dir):
            os.makedirs(self.working_dir)
        
        # Initialize LightRAG with Gemini functions
        self.rag = LightRAG(
            working_dir=self.working_dir,
            llm_model_func=gemini_llm_func,
            embedding_func=gemini_embedding_func,
            # Configure for external storage (ChromaDB + Neo4j)
            # Note: This requires custom storage adapters
        )
        
        await self.rag.initialize_storages()
        logger.info("LightRAG initialized successfully")
        
    async def insert_resume(self, resume_text: str, metadata: Dict[str, Any] = None):
        """Insert a resume into the knowledge base."""
        if not self.rag:
            await self.initialize()
        
        # Add metadata to the text for better context
        if metadata:
            enhanced_text = f"Resume: {metadata.get('filename', 'Unknown')}\n"
            enhanced_text += f"Skills: {', '.join(metadata.get('detected_skills', []))}\n"
            enhanced_text += f"Contact: {', '.join(metadata.get('emails', []))}\n\n"
            enhanced_text += resume_text
        else:
            enhanced_text = resume_text
        
        await self.rag.ainsert(enhanced_text)
        logger.info(f"Inserted resume: {metadata.get('filename', 'Unknown') if metadata else 'Unknown'}")
        
    async def search_candidates(
        self, 
        job_description: str, 
        query: str, 
        mode: str = "hybrid"
    ) -> str:
        """Search for candidates matching the job description and query."""
        if not self.rag:
            await self.initialize()
        
        # Combine job description and query for better context
        search_query = f"""
        Job Description: {job_description}
        
        Search Query: {query}
        
        Please find candidates who best match this job description and query. 
        Focus on relevant skills, experience, and qualifications.
        """
        
        result = await self.rag.aquery(
            search_query,
            param=QueryParam(mode=mode)
        )
        
        return result


# Create the Pydantic AI agent
agent = Agent(
    'openai:gpt-4o-mini',  # Fallback model for agent framework
    deps_type=RAGDeps,
    system_prompt="""You are an expert technical recruiter assistant specializing in talent matching. 
    Your role is to help recruiters find the best candidates for job positions using advanced RAG technology.
    
    When searching for candidates:
    1. Use the retrieve tool to search the resume database
    2. Analyze the job requirements carefully
    3. Match candidates based on skills, experience, and qualifications
    4. Provide clear explanations for why each candidate is a good match
    5. Rank candidates by relevance and fit
    6. Include specific examples from their resumes
    
    Always be thorough, accurate, and helpful in your responses."""
)


@agent.tool
async def retrieve(
    context: RunContext[RAGDeps], 
    job_description: str,
    search_query: str,
    mode: str = "hybrid"
) -> str:
    """Retrieve relevant candidates from the resume database.
    
    Args:
        context: The run context containing dependencies
        job_description: The full job description text
        search_query: Natural language query for candidate search
        mode: Search mode ('naive', 'local', 'global', 'hybrid')
        
    Returns:
        Formatted candidate information and matches
    """
    rag_system = TalentMatchRAG(context.deps.working_dir)
    rag_system.rag = context.deps.lightrag
    
    results = await rag_system.search_candidates(
        job_description=job_description,
        query=search_query,
        mode=mode
    )
    
    return results


async def run_talent_agent(
    job_description: str,
    query: str,
    working_dir: str = None
) -> str:
    """Run the talent matching agent.
    
    Args:
        job_description: The job description text
        query: Natural language query for candidate search
        working_dir: Working directory for LightRAG
        
    Returns:
        Agent response with candidate matches
    """
    # Initialize RAG system
    rag_system = TalentMatchRAG(working_dir)
    await rag_system.initialize()
    
    # Create dependencies
    deps = RAGDeps(
        lightrag=rag_system.rag,
        working_dir=rag_system.working_dir
    )
    
    # Prepare the full query for the agent
    full_query = f"""
    Job Description:
    {job_description}
    
    Search Request:
    {query}
    
    Please find and rank the best candidates for this position. Use the retrieve tool to search the resume database.
    """
    
    # Run the agent
    result = await agent.run(full_query, deps=deps)
    
    return result.data


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Talent Matching RAG Agent")
    parser.add_argument("--job-description", required=True, help="Job description text")
    parser.add_argument("--query", required=True, help="Search query for candidates")
    parser.add_argument("--working-dir", default=APP_CONFIG.working_dir, help="Working directory")
    
    args = parser.parse_args()
    
    # Run the agent
    response = asyncio.run(run_talent_agent(
        job_description=args.job_description,
        query=args.query,
        working_dir=args.working_dir
    ))
    
    print("\nTalent Match Results:")
    print("=" * 50)
    print(response)


if __name__ == "__main__":
    main()
