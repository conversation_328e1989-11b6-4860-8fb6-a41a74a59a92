"""Knowledge graph visualization for candidate profiles."""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import networkx as nx
import pandas as pd
from typing import Dict, List, Any, Tuple
import json
import re
from pathlib import Path

from config import APP_CONFIG


class CandidateGraphVisualizer:
    """Visualizer for candidate knowledge graphs."""
    
    def __init__(self):
        self.graph = nx.Graph()
        self.node_colors = {
            'candidate': '#FF6B6B',
            'skill': '#4ECDC4',
            'company': '#45B7D1',
            'role': '#96CEB4',
            'education': '#FFEAA7',
            'project': '#DDA0DD',
            'certification': '#98D8C8'
        }
        
    def extract_entities_from_text(self, text: str, candidate_name: str) -> Dict[str, List[str]]:
        """Extract entities from resume text for graph construction."""
        entities = {
            'skills': [],
            'companies': [],
            'roles': [],
            'education': [],
            'projects': [],
            'certifications': []
        }
        
        # Common tech skills
        tech_skills = [
            'Python', 'Java', 'JavaScript', 'React', 'Node.js', 'SQL', 'AWS', 'Docker',
            'Kubernetes', 'Git', 'Machine Learning', 'AI', 'Data Science', 'Django',
            'Flask', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'TensorFlow',
            'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn', 'HTML', 'CSS', 'TypeScript',
            'Angular', 'Vue.js', 'Spring', 'Hibernate', 'Microservices', 'REST API',
            'GraphQL', 'Jenkins', 'CI/CD', 'Agile', 'Scrum', 'Linux', 'Windows'
        ]
        
        # Extract skills
        text_lower = text.lower()
        for skill in tech_skills:
            if skill.lower() in text_lower:
                entities['skills'].append(skill)
        
        # Extract companies (look for common patterns)
        company_patterns = [
            r'(?:at|@)\s+([A-Z][a-zA-Z\s&]+(?:Inc|LLC|Corp|Ltd|Company)?)',
            r'([A-Z][a-zA-Z\s&]+(?:Inc|LLC|Corp|Ltd|Company))',
            r'(?:worked at|employed by)\s+([A-Z][a-zA-Z\s&]+)'
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            entities['companies'].extend([match.strip() for match in matches if len(match.strip()) > 2])
        
        # Extract roles/positions
        role_patterns = [
            r'(?:as|role:|position:)\s+([A-Z][a-zA-Z\s]+(?:Engineer|Developer|Manager|Analyst|Scientist|Lead|Director))',
            r'(Software Engineer|Data Scientist|Product Manager|DevOps Engineer|Full Stack Developer|Backend Developer|Frontend Developer|ML Engineer)'
        ]
        
        for pattern in role_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['roles'].extend([match.strip() for match in matches])
        
        # Extract education
        education_patterns = [
            r'(Bachelor|Master|PhD|B\.S\.|M\.S\.|Ph\.D\.)[^.]*',
            r'(University|College|Institute)[^.]*',
            r'(Computer Science|Engineering|Mathematics|Physics|Business)'
        ]
        
        for pattern in education_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['education'].extend([match.strip() for match in matches])
        
        # Remove duplicates and clean
        for key in entities:
            entities[key] = list(set([item for item in entities[key] if len(item) > 2]))[:10]  # Limit to 10 items
        
        return entities
    
    def build_candidate_graph(self, candidate_name: str, resume_text: str, metadata: Dict[str, Any] = None) -> nx.Graph:
        """Build a knowledge graph for a specific candidate."""
        self.graph.clear()
        
        # Add candidate as central node
        self.graph.add_node(candidate_name, type='candidate', size=30)
        
        # Extract entities
        entities = self.extract_entities_from_text(resume_text, candidate_name)
        
        # Add metadata if available
        if metadata:
            if 'detected_skills' in metadata:
                entities['skills'].extend(metadata['detected_skills'])
            entities['skills'] = list(set(entities['skills']))  # Remove duplicates
        
        # Add skill nodes and edges
        for skill in entities['skills'][:15]:  # Limit to top 15 skills
            self.graph.add_node(skill, type='skill', size=15)
            self.graph.add_edge(candidate_name, skill, relation='has_skill')
        
        # Add company nodes and edges
        for company in entities['companies'][:8]:  # Limit to 8 companies
            self.graph.add_node(company, type='company', size=20)
            self.graph.add_edge(candidate_name, company, relation='worked_at')
        
        # Add role nodes and edges
        for role in entities['roles'][:6]:  # Limit to 6 roles
            self.graph.add_node(role, type='role', size=18)
            self.graph.add_edge(candidate_name, role, relation='held_position')
        
        # Add education nodes and edges
        for edu in entities['education'][:5]:  # Limit to 5 education items
            self.graph.add_node(edu, type='education', size=16)
            self.graph.add_edge(candidate_name, edu, relation='studied')
        
        return self.graph
    
    def create_plotly_visualization(self, graph: nx.Graph, candidate_name: str) -> go.Figure:
        """Create a Plotly visualization of the knowledge graph."""
        # Calculate layout
        pos = nx.spring_layout(graph, k=3, iterations=50)
        
        # Prepare node traces
        node_traces = {}
        for node_type in self.node_colors.keys():
            node_traces[node_type] = {
                'x': [],
                'y': [],
                'text': [],
                'size': [],
                'hovertext': []
            }
        
        # Add nodes to traces
        for node in graph.nodes():
            node_data = graph.nodes[node]
            node_type = node_data.get('type', 'skill')
            x, y = pos[node]
            
            node_traces[node_type]['x'].append(x)
            node_traces[node_type]['y'].append(y)
            node_traces[node_type]['text'].append(node)
            node_traces[node_type]['size'].append(node_data.get('size', 10))
            
            # Create hover text
            connections = list(graph.neighbors(node))
            hover_text = f"<b>{node}</b><br>Type: {node_type}<br>Connections: {len(connections)}"
            if connections:
                hover_text += f"<br>Connected to: {', '.join(connections[:5])}"
                if len(connections) > 5:
                    hover_text += f"<br>... and {len(connections) - 5} more"
            node_traces[node_type]['hovertext'].append(hover_text)
        
        # Prepare edge trace
        edge_x = []
        edge_y = []
        edge_info = []
        
        for edge in graph.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
            
            relation = graph.edges[edge].get('relation', 'connected_to')
            edge_info.append(f"{edge[0]} → {edge[1]} ({relation})")
        
        # Create figure
        fig = go.Figure()
        
        # Add edges
        fig.add_trace(go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=1, color='#888'),
            hoverinfo='none',
            mode='lines',
            showlegend=False
        ))
        
        # Add node traces
        for node_type, trace_data in node_traces.items():
            if trace_data['x']:  # Only add if there are nodes of this type
                fig.add_trace(go.Scatter(
                    x=trace_data['x'],
                    y=trace_data['y'],
                    mode='markers+text',
                    marker=dict(
                        size=trace_data['size'],
                        color=self.node_colors[node_type],
                        line=dict(width=2, color='white')
                    ),
                    text=trace_data['text'],
                    textposition="middle center",
                    textfont=dict(size=10, color='white'),
                    hovertext=trace_data['hovertext'],
                    hoverinfo='text',
                    name=node_type.title(),
                    showlegend=True
                ))
        
        # Update layout
        fig.update_layout(
            title=f"Knowledge Graph: {candidate_name}",
            titlefont_size=16,
            showlegend=True,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="Hover over nodes to see connections",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color='gray', size=12)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white',
            height=600
        )
        
        return fig
    
    def display_graph_stats(self, graph: nx.Graph):
        """Display statistics about the knowledge graph."""
        stats = {
            "Total Nodes": graph.number_of_nodes(),
            "Total Edges": graph.number_of_edges(),
            "Node Types": {}
        }
        
        # Count nodes by type
        for node in graph.nodes():
            node_type = graph.nodes[node].get('type', 'unknown')
            stats["Node Types"][node_type] = stats["Node Types"].get(node_type, 0) + 1
        
        # Display in columns
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Nodes", stats["Total Nodes"])
        
        with col2:
            st.metric("Total Connections", stats["Total Edges"])
        
        with col3:
            st.metric("Node Types", len(stats["Node Types"]))
        
        # Display node type breakdown
        if stats["Node Types"]:
            st.subheader("Node Distribution")
            df = pd.DataFrame(list(stats["Node Types"].items()), columns=['Type', 'Count'])
            st.bar_chart(df.set_index('Type'))


def render_candidate_graph(candidate_name: str, resume_text: str, metadata: Dict[str, Any] = None):
    """Render the candidate knowledge graph in Streamlit."""
    st.subheader(f"🕸️ Knowledge Graph: {candidate_name}")
    
    # Create visualizer
    visualizer = CandidateGraphVisualizer()
    
    # Build graph
    with st.spinner("Building knowledge graph..."):
        graph = visualizer.build_candidate_graph(candidate_name, resume_text, metadata)
    
    if graph.number_of_nodes() == 0:
        st.warning("No entities found to create a knowledge graph.")
        return
    
    # Display statistics
    visualizer.display_graph_stats(graph)
    
    # Create and display visualization
    fig = visualizer.create_plotly_visualization(graph, candidate_name)
    st.plotly_chart(fig, use_container_width=True)
    
    # Display raw entities for debugging
    with st.expander("View Extracted Entities"):
        entities = visualizer.extract_entities_from_text(resume_text, candidate_name)
        for entity_type, items in entities.items():
            if items:
                st.write(f"**{entity_type.title()}:** {', '.join(items)}")
