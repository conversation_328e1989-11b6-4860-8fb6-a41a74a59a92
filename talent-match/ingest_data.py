"""Data ingestion script for processing PDF resumes into the knowledge base."""

import os
import argparse
import async<PERSON>
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

from utils import (
    get_resume_files,
    extract_text_from_pdf,
    clean_text,
    extract_resume_metadata,
    process_file_async
)
from rag_agent import TalentMatchRA<PERSON>
from config import APP_CONFIG


class ResumeIngestionPipeline:
    """Pipeline for ingesting resume files into the RAG system."""
    
    def __init__(self, working_dir: str = None, resumes_dir: str = None):
        self.working_dir = working_dir or APP_CONFIG.working_dir
        self.resumes_dir = resumes_dir or APP_CONFIG.resumes_dir
        self.rag_system = TalentMatchRAG(self.working_dir)
        self.processed_files = []
        self.failed_files = []
        
    async def initialize(self):
        """Initialize the RAG system."""
        await self.rag_system.initialize()
        print(f"RAG system initialized with working directory: {self.working_dir}")
        
    async def process_single_file(self, file_path: str) -> Dict[str, Any]:
        """Process a single resume file.
        
        Args:
            file_path: Path to the resume file
            
        Returns:
            Dictionary with processing results
        """
        try:
            print(f"Processing: {Path(file_path).name}")
            
            # Extract and process text
            text, metadata = await process_file_async(file_path)
            
            if not text.strip():
                raise ValueError("No text extracted from file")
            
            # Insert into RAG system
            await self.rag_system.insert_resume(text, metadata)
            
            result = {
                "file_path": file_path,
                "filename": metadata["filename"],
                "status": "success",
                "char_count": metadata["char_count"],
                "word_count": metadata["word_count"],
                "detected_skills": metadata["detected_skills"],
                "emails": metadata["emails"],
                "processed_at": datetime.now().isoformat()
            }
            
            self.processed_files.append(result)
            print(f"✓ Successfully processed: {metadata['filename']}")
            return result
            
        except Exception as e:
            error_result = {
                "file_path": file_path,
                "filename": Path(file_path).name,
                "status": "failed",
                "error": str(e),
                "processed_at": datetime.now().isoformat()
            }
            
            self.failed_files.append(error_result)
            print(f"✗ Failed to process: {Path(file_path).name} - {str(e)}")
            return error_result
    
    async def process_directory(self, max_concurrent: int = 3) -> Dict[str, Any]:
        """Process all resume files in the directory.
        
        Args:
            max_concurrent: Maximum number of concurrent file processing
            
        Returns:
            Summary of processing results
        """
        # Get list of resume files
        resume_files = get_resume_files(self.resumes_dir)
        
        if not resume_files:
            print(f"No resume files found in {self.resumes_dir}")
            return {
                "total_files": 0,
                "processed": 0,
                "failed": 0,
                "files": []
            }
        
        print(f"Found {len(resume_files)} resume files to process")
        
        # Process files with concurrency limit
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(file_path: str):
            async with semaphore:
                return await self.process_single_file(file_path)
        
        # Process all files
        tasks = [process_with_semaphore(file_path) for file_path in resume_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = {
                    "file_path": resume_files[i],
                    "filename": Path(resume_files[i]).name,
                    "status": "failed",
                    "error": str(result),
                    "processed_at": datetime.now().isoformat()
                }
                self.failed_files.append(error_result)
        
        return self.get_summary()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get processing summary."""
        total_files = len(self.processed_files) + len(self.failed_files)
        
        summary = {
            "total_files": total_files,
            "processed": len(self.processed_files),
            "failed": len(self.failed_files),
            "success_rate": len(self.processed_files) / total_files * 100 if total_files > 0 else 0,
            "processed_files": self.processed_files,
            "failed_files": self.failed_files,
            "timestamp": datetime.now().isoformat()
        }
        
        return summary
    
    def save_summary(self, output_file: str = None):
        """Save processing summary to file."""
        if output_file is None:
            output_file = os.path.join(self.working_dir, "ingestion_summary.json")
        
        summary = self.get_summary()
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Summary saved to: {output_file}")
    
    def print_summary(self):
        """Print processing summary to console."""
        summary = self.get_summary()
        
        print("\n" + "="*60)
        print("INGESTION SUMMARY")
        print("="*60)
        print(f"Total files: {summary['total_files']}")
        print(f"Successfully processed: {summary['processed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success rate: {summary['success_rate']:.1f}%")
        
        if summary['failed'] > 0:
            print("\nFailed files:")
            for failed in self.failed_files:
                print(f"  - {failed['filename']}: {failed['error']}")
        
        if summary['processed'] > 0:
            print("\nSuccessfully processed files:")
            for processed in self.processed_files:
                skills = processed.get('detected_skills', [])
                skills_str = ', '.join(skills[:5]) + ('...' if len(skills) > 5 else '')
                print(f"  - {processed['filename']} ({processed['word_count']} words, skills: {skills_str})")


async def main():
    """Main function for the ingestion script."""
    parser = argparse.ArgumentParser(description="Ingest resume files into the talent matching system")
    parser.add_argument("--path", default=APP_CONFIG.resumes_dir, help="Path to directory containing resume files")
    parser.add_argument("--working-dir", default=APP_CONFIG.working_dir, help="Working directory for RAG system")
    parser.add_argument("--max-concurrent", type=int, default=3, help="Maximum concurrent file processing")
    parser.add_argument("--save-summary", action="store_true", help="Save processing summary to file")
    parser.add_argument("--output-file", help="Output file for summary (default: working_dir/ingestion_summary.json)")
    
    args = parser.parse_args()
    
    # Validate paths
    if not os.path.exists(args.path):
        print(f"Error: Resume directory '{args.path}' does not exist")
        return
    
    # Initialize pipeline
    pipeline = ResumeIngestionPipeline(
        working_dir=args.working_dir,
        resumes_dir=args.path
    )
    
    try:
        # Initialize RAG system
        await pipeline.initialize()
        
        # Process files
        print(f"Starting ingestion from: {args.path}")
        await pipeline.process_directory(max_concurrent=args.max_concurrent)
        
        # Print summary
        pipeline.print_summary()
        
        # Save summary if requested
        if args.save_summary:
            pipeline.save_summary(args.output_file)
        
    except Exception as e:
        print(f"Error during ingestion: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
