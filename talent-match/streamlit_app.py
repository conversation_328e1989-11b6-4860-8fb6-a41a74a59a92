"""Streamlit application for the Talent Match RAG system."""

import streamlit as st
import asyncio
import os
import json
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Import message classes for chat interface
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    UserPromptPart,
    TextPart,
    ToolCallPart,
    ToolReturnPart,
    RetryPromptPart,
    ModelMessagesTypeAdapter
)

from rag_agent import agent, RAGDeps, TalentMatchRAG
from ingest_data import ResumeIngestionPipeline
from config import APP_CONFIG, GEMINI_CONFIG
from utils import get_resume_files, extract_text_from_pdf, clean_text, extract_resume_metadata
from graph_visualization import render_candidate_graph


# Page configuration
st.set_page_config(
    page_title="Talent Match - AI Recruiter Assistant",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)


async def get_agent_deps():
    """Initialize and return agent dependencies."""
    rag_system = TalentMatchRAG(APP_CONFIG.working_dir)
    await rag_system.initialize()
    
    return RAGDeps(
        lightrag=rag_system.rag,
        working_dir=APP_CONFIG.working_dir
    )


def display_message_part(part):
    """Display a single part of a message in the Streamlit UI."""
    if part.part_kind == 'user-prompt':
        with st.chat_message("user"):
            st.markdown(part.content)
    elif part.part_kind == 'text':
        with st.chat_message("assistant"):
            st.markdown(part.content)


async def run_agent_with_streaming(user_input: str, job_description: str):
    """Run the agent with streaming response."""
    # Combine job description and user input
    full_query = f"""
    Job Description:
    {job_description}
    
    Search Request:
    {user_input}
    
    Please find and rank the best candidates for this position using the retrieve tool.
    """
    
    async with agent.run_stream(
        full_query, 
        deps=st.session_state.agent_deps, 
        message_history=st.session_state.messages
    ) as result:
        async for message in result.stream_text(delta=True):
            yield message
    
    # Add new messages to chat history
    st.session_state.messages.extend(result.new_messages())


def sidebar_content():
    """Render sidebar content."""
    st.sidebar.title("🎯 Talent Match")
    st.sidebar.markdown("AI-Powered Recruiter Assistant")
    
    # System status
    st.sidebar.subheader("System Status")
    
    # Check if data directory exists and has content
    data_dir = Path(APP_CONFIG.working_dir)
    resumes_dir = Path(APP_CONFIG.resumes_dir)
    
    if data_dir.exists() and any(data_dir.iterdir()):
        st.sidebar.success("✅ RAG System Ready")
    else:
        st.sidebar.warning("⚠️ No data ingested yet")
    
    # Resume files status
    resume_files = get_resume_files(APP_CONFIG.resumes_dir)
    st.sidebar.info(f"📄 {len(resume_files)} resume files available")
    
    # Configuration info
    st.sidebar.subheader("Configuration")
    st.sidebar.text(f"Model: {GEMINI_CONFIG.model_name}")
    st.sidebar.text(f"Working Dir: {APP_CONFIG.working_dir}")
    st.sidebar.text(f"Resumes Dir: {APP_CONFIG.resumes_dir}")
    
    # File upload section
    st.sidebar.subheader("Upload Resumes")
    uploaded_files = st.sidebar.file_uploader(
        "Upload PDF resumes",
        type=['pdf'],
        accept_multiple_files=True,
        help="Upload PDF resume files to add to the database"
    )
    
    if uploaded_files:
        if st.sidebar.button("Process Uploaded Files"):
            process_uploaded_files(uploaded_files)
    
    # Data management
    st.sidebar.subheader("Data Management")
    
    if st.sidebar.button("Ingest All Resumes"):
        ingest_all_resumes()
    
    if st.sidebar.button("Clear Chat History"):
        st.session_state.messages = []
        st.rerun()


def process_uploaded_files(uploaded_files):
    """Process uploaded resume files."""
    if not uploaded_files:
        return
    
    # Ensure resumes directory exists
    os.makedirs(APP_CONFIG.resumes_dir, exist_ok=True)
    
    # Save uploaded files
    saved_files = []
    for uploaded_file in uploaded_files:
        file_path = os.path.join(APP_CONFIG.resumes_dir, uploaded_file.name)
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        saved_files.append(file_path)
    
    # Process files
    with st.spinner(f"Processing {len(saved_files)} uploaded files..."):
        pipeline = ResumeIngestionPipeline()
        
        async def process_files():
            await pipeline.initialize()
            results = []
            for file_path in saved_files:
                result = await pipeline.process_single_file(file_path)
                results.append(result)
            return results
        
        results = asyncio.run(process_files())
        
        # Show results
        successful = sum(1 for r in results if r.get('status') == 'success')
        failed = len(results) - successful
        
        if successful > 0:
            st.sidebar.success(f"✅ Processed {successful} files successfully")
        if failed > 0:
            st.sidebar.error(f"❌ Failed to process {failed} files")
        
        # Refresh agent deps
        st.session_state.agent_deps = asyncio.run(get_agent_deps())


def ingest_all_resumes():
    """Ingest all resumes from the resumes directory."""
    with st.spinner("Ingesting all resume files..."):
        pipeline = ResumeIngestionPipeline()
        
        async def run_ingestion():
            await pipeline.initialize()
            return await pipeline.process_directory()
        
        summary = asyncio.run(run_ingestion())
        
        # Display results
        if summary['processed'] > 0:
            st.sidebar.success(f"✅ Processed {summary['processed']} files")
        if summary['failed'] > 0:
            st.sidebar.error(f"❌ Failed: {summary['failed']} files")
        
        # Refresh agent deps
        st.session_state.agent_deps = asyncio.run(get_agent_deps())


def main_content():
    """Render main content area."""
    st.title("🎯 Talent Match - AI Recruiter Assistant")
    st.markdown("Find the perfect candidates using advanced AI-powered resume matching")

    # Create tabs for different features
    tab1, tab2 = st.tabs(["🔍 Candidate Search", "🕸️ Knowledge Graph"])

    with tab1:
        candidate_search_tab()

    with tab2:
        knowledge_graph_tab()


def candidate_search_tab():
    """Render the candidate search tab."""
    # Job description input
    st.subheader("📋 Job Description")
    job_description = st.text_area(
        "Paste the job description here:",
        height=200,
        placeholder="Enter the complete job description including required skills, experience, and qualifications...",
        key="job_description"
    )

    if not job_description.strip():
        st.warning("Please enter a job description to start searching for candidates.")
        return

    # Chat interface
    st.subheader("💬 Candidate Search")

    # Display chat history
    for msg in st.session_state.messages:
        if isinstance(msg, ModelRequest) or isinstance(msg, ModelResponse):
            for part in msg.parts:
                display_message_part(part)

    # Chat input
    user_input = st.chat_input("Ask me to find candidates (e.g., 'Find me the top 3 Python developers with AWS experience')")

    if user_input:
        # Display user message
        with st.chat_message("user"):
            st.markdown(user_input)

        # Display assistant response with streaming
        with st.chat_message("assistant"):
            message_placeholder = st.empty()
            full_response = ""

            # Stream the response
            async def stream_response():
                nonlocal full_response
                async for message in run_agent_with_streaming(user_input, job_description):
                    full_response += message
                    message_placeholder.markdown(full_response + "▌")

                # Final response without cursor
                message_placeholder.markdown(full_response)

            asyncio.run(stream_response())


def knowledge_graph_tab():
    """Render the knowledge graph visualization tab."""
    st.subheader("🕸️ Candidate Knowledge Graph Visualization")
    st.markdown("Select a candidate to visualize their skills, experience, and connections as a knowledge graph.")

    # Get available resume files
    resume_files = get_resume_files(APP_CONFIG.resumes_dir)

    if not resume_files:
        st.warning("No resume files found. Please upload some resumes first.")
        return

    # File selection
    selected_file = st.selectbox(
        "Select a candidate resume:",
        options=resume_files,
        format_func=lambda x: Path(x).name
    )

    if selected_file:
        # Extract candidate name from filename
        candidate_name = Path(selected_file).stem.replace('_', ' ').replace('-', ' ').title()

        # Load and process the resume
        with st.spinner("Loading and processing resume..."):
            try:
                # Extract text from PDF
                resume_text = extract_text_from_pdf(selected_file)
                cleaned_text = clean_text(resume_text)
                metadata = extract_resume_metadata(cleaned_text, Path(selected_file).name)

                if not cleaned_text.strip():
                    st.error("Could not extract text from the selected resume.")
                    return

                # Display basic info
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Word Count", metadata['word_count'])
                with col2:
                    st.metric("Detected Skills", len(metadata.get('detected_skills', [])))
                with col3:
                    st.metric("Email Addresses", len(metadata.get('emails', [])))

                # Render the knowledge graph
                render_candidate_graph(candidate_name, cleaned_text, metadata)

                # Show resume preview
                with st.expander("📄 Resume Preview"):
                    st.text_area("Resume Content", cleaned_text[:2000] + "..." if len(cleaned_text) > 2000 else cleaned_text, height=300)

            except Exception as e:
                st.error(f"Error processing resume: {str(e)}")
                st.exception(e)


async def main():
    """Main application function."""
    # Initialize session state
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    if "agent_deps" not in st.session_state:
        with st.spinner("Initializing AI system..."):
            st.session_state.agent_deps = await get_agent_deps()
    
    # Render UI
    sidebar_content()
    main_content()
    
    # Footer
    st.markdown("---")
    st.markdown(
        "Built with ❤️ using LightRAG, Google Gemini, and Streamlit | "
        "🔒 Your data stays secure and private"
    )


if __name__ == "__main__":
    asyncio.run(main())
