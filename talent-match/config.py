"""Configuration settings for the Talent Match application."""

import os
from dataclasses import dataclass
from typing import Optional
import dotenv

# Load environment variables
dotenv.load_dotenv()


@dataclass
class GeminiConfig:
    """Configuration for Google Gemini API."""
    api_key: str
    model_name: str = "gemini-1.5-flash"
    embedding_model: str = "models/embedding-001"
    temperature: float = 0.7
    max_tokens: int = 2048


@dataclass
class Neo4jConfig:
    """Configuration for Neo4j database."""
    uri: str
    username: str
    password: str
    database: str = "neo4j"


@dataclass
class ChromaConfig:
    """Configuration for ChromaDB."""
    host: str
    port: int
    collection_name: str = "talent_resumes"


@dataclass
class AppConfig:
    """Main application configuration."""
    working_dir: str
    resumes_dir: str
    max_file_size_mb: int = 10
    supported_file_types: list = None
    chunk_size: int = 1000
    chunk_overlap: int = 200

    def __post_init__(self):
        if self.supported_file_types is None:
            self.supported_file_types = ['.pdf', '.txt', '.docx']


def get_config() -> tuple[GeminiConfig, Neo4jConfig, ChromaConfig, AppConfig]:
    """Get all configuration objects."""
    
    # Validate required environment variables
    google_api_key = os.getenv("GOOGLE_API_KEY")
    if not google_api_key:
        raise ValueError("GOOGLE_API_KEY environment variable is required")
    
    gemini_config = GeminiConfig(
        api_key=google_api_key,
        model_name=os.getenv("GEMINI_MODEL", "gemini-1.5-flash"),
        temperature=float(os.getenv("GEMINI_TEMPERATURE", "0.7")),
        max_tokens=int(os.getenv("GEMINI_MAX_TOKENS", "2048"))
    )
    
    neo4j_config = Neo4jConfig(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        username=os.getenv("NEO4J_USERNAME", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE", "neo4j")
    )
    
    chroma_config = ChromaConfig(
        host=os.getenv("CHROMA_HOST", "localhost"),
        port=int(os.getenv("CHROMA_PORT", "8000")),
        collection_name=os.getenv("CHROMA_COLLECTION", "talent_resumes")
    )
    
    app_config = AppConfig(
        working_dir=os.getenv("WORKING_DIR", "./data"),
        resumes_dir=os.getenv("RESUMES_DIR", "./resumes"),
        max_file_size_mb=int(os.getenv("MAX_FILE_SIZE_MB", "10")),
        chunk_size=int(os.getenv("CHUNK_SIZE", "1000")),
        chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "200"))
    )
    
    return gemini_config, neo4j_config, chroma_config, app_config


# Global configuration instances
GEMINI_CONFIG, NEO4J_CONFIG, CHROMA_CONFIG, APP_CONFIG = get_config()
