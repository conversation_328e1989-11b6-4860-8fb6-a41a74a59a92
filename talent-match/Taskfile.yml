version: '3'

vars:
  NEO4J_CONTAINER_NAME: talent-match-neo4j
  CHROMA_CONTAINER_NAME: talent-match-chroma
  VENV_PATH: ../talent-match-env

tasks:
  default:
    cmds:
      - task: services:up
      - task: run
    desc: "Starts all services and runs the application."

  setup:
    desc: "Install Python dependencies from requirements.txt"
    cmds:
      - source {{.VENV_PATH}}/bin/activate && pip install --upgrade pip
      - source {{.VENV_PATH}}/bin/activate && pip install -r requirements.txt

  services:up:
    desc: "Start Neo4j and ChromaDB services using Podman."
    cmds:
      - |
        if [ ! "$(podman ps -q -f name={{.NEO4J_CONTAINER_NAME}})" ]; then
          if [ "$(podman ps -aq -f status=exited -f name={{.NEO4J_CONTAINER_NAME}})" ]; then
            echo "Starting existing Neo4j container..."
            podman start {{.NEO4J_CONTAINER_NAME}}
          else
            echo "Creating and starting Neo4j container..."
            podman run -d --name {{.NEO4J_CONTAINER_NAME}} \
              -p 7474:7474 -p 7687:7687 \
              -e NEO4J_AUTH=neo4j/password \
              -e NEO4J_PLUGINS='["apoc"]' \
              neo4j:latest
          fi
        else
          echo "Neo4j is already running."
        fi
      - |
        if [ ! "$(podman ps -q -f name={{.CHROMA_CONTAINER_NAME}})" ]; then
          if [ "$(podman ps -aq -f status=exited -f name={{.CHROMA_CONTAINER_NAME}})" ]; then
            echo "Starting existing ChromaDB container..."
            podman start {{.CHROMA_CONTAINER_NAME}}
          else
            echo "Creating and starting ChromaDB container..."
            podman run -d --name {{.CHROMA_CONTAINER_NAME}} \
              -p 8000:8000 \
              chromadb/chroma
          fi
        else
          echo "ChromaDB is already running."
        fi

  services:down:
    desc: "Stop and remove Neo4j and ChromaDB containers."
    cmds:
      - podman stop {{.NEO4J_CONTAINER_NAME}} && podman rm {{.NEO4J_CONTAINER_NAME}}
      - podman stop {{.CHROMA_CONTAINER_NAME}} && podman rm {{.CHROMA_CONTAINER_NAME}}

  services:status:
    desc: "Check status of services."
    cmds:
      - echo "Neo4j Status:"
      - podman ps -f name={{.NEO4J_CONTAINER_NAME}}
      - echo "ChromaDB Status:"
      - podman ps -f name={{.CHROMA_CONTAINER_NAME}}

  ingest:
    desc: "Run the data ingestion script to process resumes."
    deps: [setup, services:up]
    cmds:
      - source {{.VENV_PATH}}/bin/activate && python ingest_data.py --path ./resumes
    
  run:
    desc: "Run the Streamlit application."
    deps: [setup, services:up]
    cmds:
      - source {{.VENV_PATH}}/bin/activate && streamlit run streamlit_app.py

  test:
    desc: "Run tests."
    deps: [setup]
    cmds:
      - source {{.VENV_PATH}}/bin/activate && pytest tests/

  clean:
    desc: "Clean up containers and data."
    cmds:
      - task: services:down
      - rm -rf ./data/*
      - echo "Cleanup complete."
