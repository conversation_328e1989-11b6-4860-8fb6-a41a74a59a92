# Talent Match - AI-Powered Recruiter Assistant

An intelligent talent matching system that uses advanced RAG (Retrieval-Augmented Generation) technology to help recruiters find the perfect candidates for job positions.

## Features

- 🎯 **Smart Candidate Matching**: Uses LightRAG and Google Gemini to understand job requirements and match candidates
- 📄 **PDF Resume Processing**: Automatically extracts and processes information from PDF resumes
- 💬 **Natural Language Queries**: Ask questions in plain English to find candidates
- 🕸️ **Knowledge Graph Visualization**: Visualize candidate skills and experience as interactive graphs
- 🔍 **Advanced Search**: Hybrid search combining vector similarity and knowledge graph traversal
- 📊 **Real-time Analytics**: Track ingestion progress and system status

## Architecture

- **Frontend**: Streamlit web application
- **Backend**: LangGraph + LightRAG + Google Gemini 1.5 Flash
- **Storage**: ChromaDB (vector storage) + Neo4j (knowledge graph)
- **Orchestration**: Taskfile with Podman containers

## Prerequisites

- Python 3.11+
- Podman (or Docker)
- Google Gemini API key
- Task CLI tool (`brew install go-task/tap/go-task` on macOS)

## Setup Instructions

### 1. <PERSON>lone and Setup Environment

```bash
# Navigate to the project directory
cd talent-match

# Activate the virtual environment
source ../talent-match-env/bin/activate

# Install dependencies
task setup
```

### 2. Configure Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your Google Gemini API key
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here
```

### 3. Start Services

```bash
# Start Neo4j and ChromaDB containers
task services:up

# Check service status
task services:status
```

### 4. Add Resume Files

```bash
# Place PDF resume files in the resumes directory
mkdir -p resumes
# Copy your PDF resumes to the resumes/ directory
```

### 5. Ingest Resume Data

```bash
# Process all resumes in the resumes directory
task ingest
```

### 6. Run the Application

```bash
# Start the Streamlit application
task run
```

The application will be available at `http://localhost:8501`

## Usage

### 1. Candidate Search

1. **Enter Job Description**: Paste the complete job description in the text area
2. **Ask Natural Language Questions**: Use the chat interface to search for candidates
   - Example: "Find me the top 3 Python developers with AWS experience"
   - Example: "Who has machine learning experience and has worked at startups?"
3. **Review Results**: Get ranked candidates with explanations and relevant experience

### 2. Knowledge Graph Visualization

1. **Select Candidate**: Choose a resume from the dropdown
2. **View Graph**: See the candidate's skills, companies, and experience as an interactive graph
3. **Explore Connections**: Hover over nodes to see relationships and connections

## Task Commands

```bash
# Setup and dependencies
task setup                 # Install Python dependencies
task services:up          # Start Neo4j and ChromaDB
task services:down        # Stop and remove containers
task services:status      # Check service status

# Data management
task ingest               # Process resumes from ./resumes directory
task clean                # Clean up containers and data

# Application
task run                  # Start Streamlit app
task test                 # Run tests (when available)
```

## Configuration

Key configuration options in `.env`:

```bash
# Google Gemini API
GOOGLE_API_KEY=your_api_key
GEMINI_MODEL=gemini-1.5-flash
GEMINI_TEMPERATURE=0.7

# Database connections
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
CHROMA_HOST=localhost
CHROMA_PORT=8000

# Application settings
WORKING_DIR=./data
RESUMES_DIR=./resumes
MAX_FILE_SIZE_MB=10
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

## File Structure

```
talent-match/
├── streamlit_app.py          # Main Streamlit application
├── rag_agent.py              # RAG agent with LangGraph + LightRAG
├── ingest_data.py            # Resume ingestion pipeline
├── graph_visualization.py    # Knowledge graph visualization
├── config.py                 # Configuration management
├── utils.py                  # Utility functions
├── Taskfile.yml             # Task automation
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── resumes/                 # Directory for PDF resumes
└── data/                    # LightRAG working directory
```

## Troubleshooting

### Common Issues

1. **"No module named 'lightrag'"**
   - Make sure you've activated the virtual environment and run `task setup`

2. **"GOOGLE_API_KEY not found"**
   - Ensure you've created a `.env` file with your Google Gemini API key

3. **"Connection refused" errors**
   - Check that Neo4j and ChromaDB containers are running: `task services:status`
   - Restart services: `task services:down && task services:up`

4. **"No resume files found"**
   - Place PDF files in the `resumes/` directory
   - Ensure files are valid PDFs under 10MB

### Service URLs

- **Streamlit App**: http://localhost:8501
- **Neo4j Browser**: http://localhost:7474 (neo4j/password)
- **ChromaDB**: http://localhost:8000

## Development

### Adding New Features

1. **Custom Skills Extraction**: Modify `utils.py` to add domain-specific skill detection
2. **Additional File Types**: Extend `utils.py` to support DOCX, TXT files
3. **Advanced Visualizations**: Enhance `graph_visualization.py` with new chart types
4. **Custom Agents**: Create specialized agents in `rag_agent.py` for different roles

### Testing

```bash
# Run the ingestion pipeline with verbose output
python ingest_data.py --path ./resumes --save-summary

# Test the RAG agent directly
python rag_agent.py --job-description "Senior Python Developer" --query "Find experienced candidates"
```

## License

This project is built for educational and demonstration purposes. Please ensure you have appropriate permissions for any resume data you process.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the logs in the terminal where you ran the commands
3. Ensure all prerequisites are properly installed
