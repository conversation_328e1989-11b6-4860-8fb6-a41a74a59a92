"""Utility functions for PDF processing and data handling."""

import os
import hashlib
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import asyncio

import PyPDF2
import pdfplumber
import pandas as pd
from config import APP_CONFIG


def extract_text_from_pdf(pdf_path: str, method: str = "pdfplumber") -> str:
    """Extract text from PDF file using specified method.
    
    Args:
        pdf_path: Path to the PDF file
        method: Extraction method ('pdfplumber' or 'pypdf2')
        
    Returns:
        Extracted text content
    """
    try:
        if method == "pdfplumber":
            return _extract_with_pdfplumber(pdf_path)
        elif method == "pypdf2":
            return _extract_with_pypdf2(pdf_path)
        else:
            raise ValueError(f"Unsupported extraction method: {method}")
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {e}")
        return ""


def _extract_with_pdfplumber(pdf_path: str) -> str:
    """Extract text using pdfplumber (better for complex layouts)."""
    text = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    return text


def _extract_with_pypdf2(pdf_path: str) -> str:
    """Extract text using PyPDF2 (faster but less accurate)."""
    text = ""
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
    return text


def clean_text(text: str) -> str:
    """Clean and normalize extracted text.
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\.\,\;\:\!\?\-\(\)]', ' ', text)
    
    # Remove multiple consecutive punctuation
    text = re.sub(r'[\.]{2,}', '.', text)
    text = re.sub(r'[\-]{2,}', '-', text)
    
    # Normalize line breaks
    text = text.replace('\n', ' ').replace('\r', ' ')
    
    return text.strip()


def extract_resume_metadata(text: str, filename: str) -> Dict[str, Any]:
    """Extract metadata from resume text.
    
    Args:
        text: Resume text content
        filename: Original filename
        
    Returns:
        Dictionary containing extracted metadata
    """
    metadata = {
        "filename": filename,
        "char_count": len(text),
        "word_count": len(text.split()),
        "file_hash": hashlib.md5(text.encode()).hexdigest()
    }
    
    # Extract email addresses
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    metadata["emails"] = emails[:3]  # Limit to first 3 emails
    
    # Extract phone numbers (basic pattern)
    phone_pattern = r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
    phones = re.findall(phone_pattern, text)
    metadata["phones"] = [phone[0] + phone[1] if isinstance(phone, tuple) else phone for phone in phones[:2]]
    
    # Extract potential skills (common tech keywords)
    tech_skills = [
        'Python', 'Java', 'JavaScript', 'React', 'Node.js', 'SQL', 'AWS', 'Docker',
        'Kubernetes', 'Git', 'Machine Learning', 'AI', 'Data Science', 'Django',
        'Flask', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'TensorFlow',
        'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn', 'HTML', 'CSS', 'TypeScript'
    ]
    
    found_skills = []
    text_lower = text.lower()
    for skill in tech_skills:
        if skill.lower() in text_lower:
            found_skills.append(skill)
    
    metadata["detected_skills"] = found_skills[:10]  # Limit to top 10
    
    return metadata


def chunk_text(text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
    """Split text into overlapping chunks.
    
    Args:
        text: Text to chunk
        chunk_size: Size of each chunk (defaults to config value)
        overlap: Overlap between chunks (defaults to config value)
        
    Returns:
        List of text chunks
    """
    if chunk_size is None:
        chunk_size = APP_CONFIG.chunk_size
    if overlap is None:
        overlap = APP_CONFIG.chunk_overlap
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = min(start + chunk_size, len(text))
        
        # Try to find a good break point (sentence or paragraph end)
        if end < len(text):
            # Look for sentence endings
            sentence_end = text.rfind('.', start, end)
            if sentence_end > start + chunk_size // 2:
                end = sentence_end + 1
            else:
                # Look for word boundaries
                word_end = text.rfind(' ', start, end)
                if word_end > start + chunk_size // 2:
                    end = word_end
        
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        # Move start position with overlap
        start = max(end - overlap, start + 1)
        
        if start >= len(text):
            break
    
    return chunks


def get_resume_files(directory: str) -> List[str]:
    """Get list of resume files from directory.
    
    Args:
        directory: Directory to scan for resume files
        
    Returns:
        List of file paths
    """
    resume_files = []
    directory_path = Path(directory)
    
    if not directory_path.exists():
        print(f"Directory {directory} does not exist")
        return resume_files
    
    for file_path in directory_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in APP_CONFIG.supported_file_types:
            # Check file size
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb <= APP_CONFIG.max_file_size_mb:
                resume_files.append(str(file_path))
            else:
                print(f"Skipping {file_path.name}: file too large ({file_size_mb:.1f}MB)")
    
    return sorted(resume_files)


def format_candidate_results(results: List[Dict[str, Any]]) -> str:
    """Format candidate search results for display.
    
    Args:
        results: List of candidate result dictionaries
        
    Returns:
        Formatted string for display
    """
    if not results:
        return "No candidates found matching the criteria."
    
    formatted = "## Top Candidate Matches\n\n"
    
    for i, candidate in enumerate(results, 1):
        formatted += f"### {i}. {candidate.get('filename', 'Unknown Candidate')}\n"
        formatted += f"**Relevance Score:** {candidate.get('score', 'N/A')}\n"
        formatted += f"**Key Skills:** {', '.join(candidate.get('skills', []))}\n"
        formatted += f"**Match Explanation:** {candidate.get('explanation', 'No explanation available')}\n"
        
        if candidate.get('contact_info'):
            formatted += f"**Contact:** {candidate.get('contact_info')}\n"
        
        formatted += "\n---\n\n"
    
    return formatted


async def process_file_async(file_path: str) -> Tuple[str, Dict[str, Any]]:
    """Asynchronously process a single resume file.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Tuple of (processed_text, metadata)
    """
    def _process_sync():
        text = extract_text_from_pdf(file_path)
        cleaned_text = clean_text(text)
        metadata = extract_resume_metadata(cleaned_text, Path(file_path).name)
        return cleaned_text, metadata
    
    # Run the synchronous processing in a thread pool
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, _process_sync)
