"""RAG Agent implementation using Google Gemini and simple vector search."""

import os
import sys
import argparse
import asyncio
from dataclasses import dataclass
from typing import Optional, Dict, Any, List
import json
import logging

import google.generativeai as genai
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent

from config import GEMINI_CONFIG, NEO4J_CONFIG, CHROMA_CONFIG, APP_CONFIG

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Configure Gemini API
genai.configure(api_key=GEMINI_CONFIG.api_key)


async def gemini_llm_func(
    prompt: str,
    system_prompt: str = None,
    history_messages: List[Dict] = None,
    **kwargs
) -> str:
    """Custom LLM function for LightRAG using Google Gemini."""
    try:
        model = genai.GenerativeModel(GEMINI_CONFIG.model_name)
        
        # Prepare the full prompt
        full_prompt = ""
        if system_prompt:
            full_prompt += f"System: {system_prompt}\n\n"
        
        if history_messages:
            for msg in history_messages:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                full_prompt += f"{role.capitalize()}: {content}\n"
        
        full_prompt += f"User: {prompt}\n\nAssistant:"
        
        # Generate response
        response = await model.generate_content_async(
            full_prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=GEMINI_CONFIG.temperature,
                max_output_tokens=GEMINI_CONFIG.max_tokens,
            )
        )
        
        return response.text
        
    except Exception as e:
        logger.error(f"Error in Gemini LLM function: {e}")
        return f"Error generating response: {str(e)}"


async def gemini_embedding_func(texts: List[str]) -> List[List[float]]:
    """Custom embedding function for LightRAG using Google Gemini."""
    try:
        embeddings = []
        
        for text in texts:
            result = genai.embed_content(
                model=GEMINI_CONFIG.embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            embeddings.append(result['embedding'])
        
        return embeddings
        
    except Exception as e:
        logger.error(f"Error in Gemini embedding function: {e}")
        # Return zero embeddings as fallback
        return [[0.0] * 768 for _ in texts]


@dataclass
class RAGDeps:
    """Dependencies for the RAG agent."""
    resume_data: List[Dict[str, Any]]
    working_dir: str


class TalentMatchRAG:
    """Talent matching RAG system using simple vector search and Gemini."""

    def __init__(self, working_dir: str = None):
        self.working_dir = working_dir or APP_CONFIG.working_dir
        self.resume_data = []

    async def initialize(self):
        """Initialize the RAG system."""
        if not os.path.exists(self.working_dir):
            os.makedirs(self.working_dir)

        logger.info("Simple RAG system initialized successfully")

    async def insert_resume(self, resume_text: str, metadata: Dict[str, Any] = None):
        """Insert a resume into the knowledge base."""
        # Add metadata to the text for better context
        if metadata:
            enhanced_text = f"Resume: {metadata.get('filename', 'Unknown')}\n"
            enhanced_text += f"Skills: {', '.join(metadata.get('detected_skills', []))}\n"
            enhanced_text += f"Contact: {', '.join(metadata.get('emails', []))}\n\n"
            enhanced_text += resume_text
        else:
            enhanced_text = resume_text

        # Store in simple list for now
        self.resume_data.append({
            'text': enhanced_text,
            'metadata': metadata or {},
            'filename': metadata.get('filename', 'Unknown') if metadata else 'Unknown'
        })

        logger.info(f"Inserted resume: {metadata.get('filename', 'Unknown') if metadata else 'Unknown'}")

    async def search_candidates(
        self,
        job_description: str,
        query: str,
        mode: str = "simple"
    ) -> str:
        """Search for candidates matching the job description and query."""
        if not self.resume_data:
            return "No candidates found in the database. Please ingest some resumes first."

        # Simple keyword-based search for now
        search_terms = query.lower().split() + job_description.lower().split()

        # Score candidates based on keyword matches
        scored_candidates = []
        for resume in self.resume_data:
            text_lower = resume['text'].lower()
            score = sum(1 for term in search_terms if term in text_lower)
            if score > 0:
                scored_candidates.append({
                    'resume': resume,
                    'score': score
                })

        # Sort by score
        scored_candidates.sort(key=lambda x: x['score'], reverse=True)

        # Format results
        if not scored_candidates:
            return "No candidates found matching the criteria."

        result = "## Top Candidate Matches\n\n"
        for i, candidate in enumerate(scored_candidates[:5], 1):
            resume = candidate['resume']
            result += f"### {i}. {resume['filename']}\n"
            result += f"**Match Score:** {candidate['score']}\n"
            result += f"**Skills:** {', '.join(resume['metadata'].get('detected_skills', []))}\n"
            result += f"**Contact:** {', '.join(resume['metadata'].get('emails', []))}\n\n"

        return result


# Create the Pydantic AI agent
agent = Agent(
    'gemini-1.5-flash',  # Use Gemini model
    deps_type=RAGDeps,
    system_prompt="""You are an expert technical recruiter assistant specializing in talent matching.
    Your role is to help recruiters find the best candidates for job positions.

    When searching for candidates:
    1. Use the retrieve tool to search the resume database
    2. Analyze the job requirements carefully
    3. Match candidates based on skills, experience, and qualifications
    4. Provide clear explanations for why each candidate is a good match
    5. Rank candidates by relevance and fit
    6. Include specific examples from their resumes

    Always be thorough, accurate, and helpful in your responses."""
)


@agent.tool
async def retrieve(
    context: RunContext[RAGDeps],
    job_description: str,
    search_query: str,
    mode: str = "simple"
) -> str:
    """Retrieve relevant candidates from the resume database.

    Args:
        context: The run context containing dependencies
        job_description: The full job description text
        search_query: Natural language query for candidate search
        mode: Search mode (currently only 'simple' supported)

    Returns:
        Formatted candidate information and matches
    """
    rag_system = TalentMatchRAG(context.deps.working_dir)
    rag_system.resume_data = context.deps.resume_data

    results = await rag_system.search_candidates(
        job_description=job_description,
        query=search_query,
        mode=mode
    )

    return results


async def run_talent_agent(
    job_description: str,
    query: str,
    working_dir: str = None,
    resume_data: List[Dict[str, Any]] = None
) -> str:
    """Run the talent matching agent.

    Args:
        job_description: The job description text
        query: Natural language query for candidate search
        working_dir: Working directory for the system
        resume_data: List of resume data

    Returns:
        Agent response with candidate matches
    """
    # Initialize RAG system
    rag_system = TalentMatchRAG(working_dir)
    await rag_system.initialize()

    # Use provided resume data or empty list
    if resume_data:
        rag_system.resume_data = resume_data

    # Create dependencies
    deps = RAGDeps(
        resume_data=rag_system.resume_data,
        working_dir=rag_system.working_dir
    )

    # For now, return simple search results directly
    results = await rag_system.search_candidates(
        job_description=job_description,
        query=query
    )

    return results


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Talent Matching RAG Agent")
    parser.add_argument("--job-description", required=True, help="Job description text")
    parser.add_argument("--query", required=True, help="Search query for candidates")
    parser.add_argument("--working-dir", default=APP_CONFIG.working_dir, help="Working directory")
    
    args = parser.parse_args()
    
    # Run the agent
    response = asyncio.run(run_talent_agent(
        job_description=args.job_description,
        query=args.query,
        working_dir=args.working_dir
    ))
    
    print("\nTalent Match Results:")
    print("=" * 50)
    print(response)


if __name__ == "__main__":
    main()
