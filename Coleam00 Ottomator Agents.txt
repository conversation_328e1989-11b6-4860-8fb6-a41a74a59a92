Directory structure:
└── light-rag-agent/
    ├── README.md
    ├── BasicRAG/
    │   ├── insert_pydantic_docs.py
    │   ├── rag_agent.py
    │   ├── requirements.txt
    │   ├── streamlit_app.py
    │   └── utils.py
    └── LightRAG/
        ├── insert_pydantic_docs.py
        ├── rag_agent.py
        ├── requirements.txt
        ├── streamlit_app.py
        ├── super-basic-lightrag.py
        └── .env.example

================================================
FILE: light-rag-agent/README.md
================================================
# LightRAG vs BasicRAG: Comparing RAG Implementations

This project demonstrates two different implementations of Retrieval-Augmented Generation (RAG) for answering questions about Pydantic AI using its documentation:

1. **BasicRAG**: A traditional RAG implementation using ChromaDB for vector storage and similarity search
2. **LightRAG**: An advanced, lightweight RAG implementation with enhanced knowledge graph capabilities

## Project Goal

The primary goal of this project is to showcase the power and efficiency of LightRAG compared to traditional RAG implementations. LightRAG offers several advantages:

- **Simplified API**: LightRAG provides a more streamlined API with fewer configuration parameters
- **Automatic Document Processing**: LightRAG handles document chunking and embedding automatically
- **Knowledge Graph Integration**: LightRAG leverages knowledge graph capabilities for improved context understanding
- **More Efficient Retrieval**: LightRAG's query mechanism provides more relevant results with less configuration

## Installation

### Prerequisites
- Python 3.11+
- OpenAI API key

### Setup

1. Clone this repository

2. Create a `.env` file in both the `BasicRAG` and `LightRAG` directories (or whichever you want to use) with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

3. Set up a virtual environment and install dependencies:

   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # Windows
   .\venv\Scripts\activate
   # macOS/Linux
   source venv/bin/activate
   
   # Install dependencies for LightRAG
   cd LightRAG
   pip install -r requirements.txt
   
   # In a new terminal with activated venv, install BasicRAG dependencies
   cd BasicRAG
   pip install -r requirements.txt
   ```

## Running the Implementations

### LightRAG (Most Powerful)

1. **Insert Documentation** (this will take a while - using full Pydantic AI docs as an example!):
   ```bash
   cd LightRAG
   python insert_pydantic_docs.py
   ```
   This will fetch the Pydantic AI documentation and process it using LightRAG's advanced document processing.

2. **Run the Agent**:
   ```bash
   python rag_agent.py --question "How do I create a Pydantic AI agent?"
   ```

3. **Run the Interactive Streamlit App**:
   ```bash
   streamlit run streamlit_app.py
   ```
   This provides a chat interface where you can ask questions about Pydantic AI.

### BasicRAG

1. **Insert Documentation** (this will take a while - using full Pydantic AI docs as an example!):
   ```bash
   cd BasicRAG
   python insert_pydantic_docs.py
   ```
   This will fetch and process the Pydantic AI documentation into ChromaDB with manual chunking.

2. **Run the Agent**:
   ```bash
   python rag_agent.py --question "How do I create a Pydantic AI agent?"
   ```
   You can customize the number of results from the vector DB with `--n-results 10`.

3. **Run the Interactive Streamlit App**:
   ```bash
   streamlit run streamlit_app.py
   ```

## Key Differences Between Implementations

### Document Processing
- **BasicRAG**: Manually splits documents into chunks with specified size and overlap, requiring careful tuning
- **LightRAG**: Automatically handles document processing with intelligent chunking

### Vector Storage
- **BasicRAG**: Uses ChromaDB directly with manual collection management
- **LightRAG**: Abstracts storage details behind a clean API with optimized defaults

### Query Mechanism
- **BasicRAG**: Requires specifying the number of results to return
- **LightRAG**: Uses a more sophisticated query mechanism with different modes (e.g., "naive" or "hybrid")

### Code Complexity
- **BasicRAG**: Requires more boilerplate code for setting up collections and processing documents
- **LightRAG**: Offers a more concise API with fewer lines of code needed

## Project Structure

### LightRAG
- `LightRAG/rag_agent.py`: Pydantic AI agent using LightRAG
- `LightRAG/insert_pydantic_docs.py`: Script to fetch and process documentation
- `LightRAG/streamlit_app.py`: Interactive web interface

### BasicRAG
- `BasicRAG/rag_agent.py`: Pydantic AI agent using traditional RAG with ChromaDB
- `BasicRAG/insert_pydantic_docs.py`: Script for document processing with manual chunking
- `BasicRAG/utils.py`: Utility functions for ChromaDB operations
- `BasicRAG/streamlit_app.py`: Interactive web interface

## Comparing Performance

To compare the performance of both implementations:

1. Run both Streamlit apps (in separate terminals)
2. Ask the same questions to both agents
3. Compare the quality and relevance of responses
4. Note the differences in response time and accuracy

LightRAG typically provides more contextually relevant answers with less configuration, demonstrating the advantages of its enhanced knowledge graph capabilities and optimized retrieval mechanisms.



================================================
FILE: light-rag-agent/BasicRAG/insert_pydantic_docs.py
================================================
"""Script to insert Pydantic AI documentation from URL into ChromaDB."""

import argparse
import hashlib
import os
import re
from typing import List, Dict, Any, Tuple
import httpx

from utils import get_chroma_client, get_or_create_collection, add_documents_to_collection

# URL of the Pydantic AI documentation
PYDANTIC_DOCS_URL = "https://ai.pydantic.dev/llms.txt"

def fetch_pydantic_docs() -> str:
    """Fetch the Pydantic AI documentation from the URL.
    
    Returns:
        The content of the documentation
    """
    try:
        response = httpx.get(PYDANTIC_DOCS_URL)
        response.raise_for_status()
        return response.text
    except Exception as e:
        raise Exception(f"Error fetching Pydantic AI documentation: {e}")

def split_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Split the text into overlapping chunks.
    
    Args:
        text: The text to split
        chunk_size: The size of each chunk
        overlap: The overlap between chunks
        
    Returns:
        List of text chunks
    """
    chunks = []
    start = 0
    
    while start < len(text):
        # Calculate end position for this chunk
        end = min(start + chunk_size, len(text))
        
        # If we're not at the end of the text, try to find a good break point
        if end < len(text):
            # Look for a newline character to break at
            newline_pos = text.rfind('\n', start, end)
            if newline_pos > start + chunk_size // 2:  # Only use if it's not too close to the start
                end = newline_pos + 1  # Include the newline
        
        # Add the chunk to our list
        chunks.append(text[start:end])
        
        # Move the start position for the next chunk, considering overlap
        # Ensure we always make progress by moving at least 1 character forward
        start = max(end - overlap, start + 1)
        
        # Safety check to ensure we're making progress
        if start >= len(text):
            break
    
    return chunks

def extract_section_info(chunk: str) -> Dict[str, Any]:
    """Extract section information from a chunk.
    
    Args:
        chunk: The text chunk
        
    Returns:
        Dictionary with section information
    """
    # Try to extract section headers
    headers = re.findall(r'headers:\{type:MARKDOWN_NODE_TYPE_HEADER_\d+ text:"([^"]+)"\}', chunk)
    
    # Extract content type if available
    content_type_match = re.search(r'type:"([^"]+)"', chunk)
    content_type = content_type_match.group(1) if content_type_match else "unknown"
    
    return {
        "headers": "; ".join(headers) if headers else "",  
        "content_type": content_type,
        "char_count": len(chunk),
        "word_count": len(chunk.split())
    }

def process_pydantic_docs(chunk_size: int = 1000, overlap: int = 200) -> Tuple[List[str], List[str], List[Dict[str, Any]]]:
    """Process the Pydantic AI documentation.
    
    Args:
        chunk_size: The size of each chunk
        overlap: The overlap between chunks
        
    Returns:
        Tuple containing lists of IDs, documents, and metadatas
    """
    print("Fetching Pydantic AI documentation...")
    content = fetch_pydantic_docs()
    
    print(f"Splitting documentation into chunks (size: {chunk_size}, overlap: {overlap})...")
    chunks = split_into_chunks(content, chunk_size, overlap)
    
    ids = []
    documents = []
    metadatas = []
    
    print(f"Processing {len(chunks)} chunks...")
    for i, chunk in enumerate(chunks):
        # Generate a unique ID for the chunk
        chunk_id = f"pydantic-docs-chunk-{i}"
        
        # Extract metadata
        metadata = extract_section_info(chunk)
        metadata["chunk_index"] = i
        metadata["source"] = PYDANTIC_DOCS_URL
        
        # Add to our lists
        ids.append(chunk_id)
        documents.append(chunk)
        metadatas.append(metadata)
        
        if i % 10 == 0:
            print(f"Processed {i} chunks...")
    
    return ids, documents, metadatas

def main():
    """Main function to parse arguments and insert Pydantic docs into ChromaDB."""
    parser = argparse.ArgumentParser(description="Insert Pydantic AI documentation into ChromaDB")
    parser.add_argument("--collection", default="pydantic_docs", help="Name of the ChromaDB collection")
    parser.add_argument("--db-dir", default="./chroma_db", help="Directory to store ChromaDB data")
    parser.add_argument("--embedding-model", default="all-MiniLM-L6-v2", 
                        help="Name of the embedding model to use")
    parser.add_argument("--chunk-size", type=int, default=1000, help="Size of each text chunk")
    parser.add_argument("--overlap", type=int, default=200, help="Overlap between chunks")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for adding documents")
    
    args = parser.parse_args()
    
    # Process Pydantic AI documentation
    print("Processing Pydantic AI documentation...")
    ids, documents, metadatas = process_pydantic_docs(
        chunk_size=args.chunk_size,
        overlap=args.overlap
    )
    
    if not documents:
        print("No documents found to process.")
        return
    
    print(f"Found {len(documents)} chunks.")
    
    # Get ChromaDB client and collection
    print(f"Connecting to ChromaDB at {args.db_dir}...")
    client = get_chroma_client(args.db_dir)
    collection = get_or_create_collection(
        client, 
        args.collection,
        embedding_model_name=args.embedding_model
    )
    
    # Add documents to the collection
    print(f"Adding chunks to collection '{args.collection}'...")
    add_documents_to_collection(
        collection,
        ids,
        documents,
        metadatas,
        batch_size=args.batch_size
    )
    
    print(f"Successfully added {len(documents)} chunks to ChromaDB collection '{args.collection}'.")

if __name__ == "__main__":
    main()



================================================
FILE: light-rag-agent/BasicRAG/rag_agent.py
================================================
"""Pydantic AI agent that leverages RAG with a local ChromaDB for Pydantic documentation."""

import os
import sys
import argparse
from dataclasses import dataclass
from typing import Optional
import asyncio
import chromadb

import dotenv
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent
from openai import AsyncOpenAI

from utils import (
    get_chroma_client,
    get_or_create_collection,
    query_collection,
    format_results_as_context
)

# Load environment variables from .env file
dotenv.load_dotenv()

# Check for OpenAI API key
if not os.getenv("OPENAI_API_KEY"):
    print("Error: OPENAI_API_KEY environment variable not set.")
    print("Please create a .env file with your OpenAI API key or set it in your environment.")
    sys.exit(1)


@dataclass
class RAGDeps:
    """Dependencies for the RAG agent."""
    chroma_client: chromadb.PersistentClient
    collection_name: str
    embedding_model: str


# Create the Pydantic AI agent
agent = Agent(
    'openai:gpt-4o-mini',
    deps_type=RAGDeps,
    system_prompt="You are a helpful assistant that answers questions about Pydantic AI based on the provided documentation. "
                  "Use the retrieve tool to get relevant information from the Pydantic AI documentation before answering. "
                  "If the documentation doesn't contain the answer, clearly state that the information isn't available "
                  "in the current documentation and provide your best general knowledge response."
)


@agent.tool
async def retrieve(context: RunContext[RAGDeps], search_query: str, n_results: int = 5) -> str:
    """Retrieve relevant documents from ChromaDB based on a search query.
    
    Args:
        context: The run context containing dependencies.
        search_query: The search query to find relevant documents.
        n_results: Number of results to return (default: 5).
        
    Returns:
        Formatted context information from the retrieved documents.
    """
    # Get ChromaDB client and collection
    collection = get_or_create_collection(
        context.deps.chroma_client,
        context.deps.collection_name,
        embedding_model_name=context.deps.embedding_model
    )
    
    # Query the collection
    query_results = query_collection(
        collection,
        search_query,
        n_results=n_results
    )
    
    # Format the results as context
    return format_results_as_context(query_results)


async def run_rag_agent(
    question: str,
    collection_name: str = "pydantic_docs",
    db_directory: str = "./chroma_db",
    embedding_model: str = "all-MiniLM-L6-v2",
    n_results: int = 5
) -> str:
    """Run the RAG agent to answer a question about Pydantic AI.
    
    Args:
        question: The question to answer.
        collection_name: Name of the ChromaDB collection to use.
        db_directory: Directory where ChromaDB data is stored.
        embedding_model: Name of the embedding model to use.
        n_results: Number of results to return from the retrieval.
        
    Returns:
        The agent's response.
    """
    # Create dependencies
    deps = RAGDeps(
        chroma_client=get_chroma_client(db_directory),
        collection_name=collection_name,
        embedding_model=embedding_model
    )
    
    # Run the agent
    result = await agent.run(question, deps=deps)
    
    return result.data


def main():
    """Main function to parse arguments and run the RAG agent."""
    parser = argparse.ArgumentParser(description="Run a Pydantic AI agent with RAG using ChromaDB")
    parser.add_argument("--question", help="The question to answer about Pydantic AI")
    parser.add_argument("--collection", default="pydantic_docs", help="Name of the ChromaDB collection")
    parser.add_argument("--db-dir", default="./chroma_db", help="Directory where ChromaDB data is stored")
    parser.add_argument("--embedding-model", default="all-MiniLM-L6-v2", help="Name of the embedding model to use")
    parser.add_argument("--n-results", type=int, default=5, help="Number of results to return from the retrieval")
    
    args = parser.parse_args()
    
    # Run the agent
    response = asyncio.run(run_rag_agent(
        args.question,
        collection_name=args.collection,
        db_directory=args.db_dir,
        embedding_model=args.embedding_model,
        n_results=args.n_results
    ))
    
    print("\nResponse:")
    print(response)


if __name__ == "__main__":
    main()



================================================
FILE: light-rag-agent/BasicRAG/requirements.txt
================================================
[Binary file]


================================================
FILE: light-rag-agent/BasicRAG/streamlit_app.py
================================================
from dotenv import load_dotenv
import streamlit as st
import asyncio
import os

# Import all the message part classes
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    UserPromptPart,
    TextPart,
    ToolCallPart,
    ToolReturnPart,
    RetryPromptPart,
    ModelMessagesTypeAdapter
)

from rag_agent import agent, RAGDeps
from utils import get_chroma_client

load_dotenv()

async def get_agent_deps():
    """
    Creates a LightRAG instance
    And then uses that to create the Pydantic AI agent dependencies.
    """
    return RAGDeps(
        chroma_client=get_chroma_client("./chroma_db"),
        collection_name="pydantic_docs",
        embedding_model="all-MiniLM-L6-v2"
    )


def display_message_part(part):
    """
    Display a single part of a message in the Streamlit UI.
    Customize how you display system prompts, user prompts,
    tool calls, tool returns, etc.
    """
    # user-prompt
    if part.part_kind == 'user-prompt':
        with st.chat_message("user"):
            st.markdown(part.content)
    # text
    elif part.part_kind == 'text':
        with st.chat_message("assistant"):
            st.markdown(part.content)             

async def run_agent_with_streaming(user_input):
    async with agent.run_stream(
        user_input, deps=st.session_state.agent_deps, message_history=st.session_state.messages
    ) as result:
        async for message in result.stream_text(delta=True):  
            yield message

    # Add the new messages to the chat history (including tool calls and responses)
    st.session_state.messages.extend(result.new_messages())


# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~ Main Function with UI Creation ~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

async def main():
    st.title("ChromaDB Basic RAG AI Agent")

    # Initialize chat history in session state if not present
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "agent_deps" not in st.session_state:
        st.session_state.agent_deps = await get_agent_deps()  

    # Display all messages from the conversation so far
    # Each message is either a ModelRequest or ModelResponse.
    # We iterate over their parts to decide how to display them.
    for msg in st.session_state.messages:
        if isinstance(msg, ModelRequest) or isinstance(msg, ModelResponse):
            for part in msg.parts:
                display_message_part(part)

    # Chat input for the user
    user_input = st.chat_input("What do you want to know?")

    if user_input:
        # Display user prompt in the UI
        with st.chat_message("user"):
            st.markdown(user_input)

        # Display the assistant's partial response while streaming
        with st.chat_message("assistant"):
            # Create a placeholder for the streaming text
            message_placeholder = st.empty()
            full_response = ""
            
            # Properly consume the async generator with async for
            generator = run_agent_with_streaming(user_input)
            async for message in generator:
                full_response += message
                message_placeholder.markdown(full_response + "▌")
            
            # Final response without the cursor
            message_placeholder.markdown(full_response)


if __name__ == "__main__":
    asyncio.run(main())



================================================
FILE: light-rag-agent/BasicRAG/utils.py
================================================
"""Utility functions for text processing and ChromaDB operations."""

import os
import pathlib
from typing import List, Dict, Any, Optional

import chromadb
from chromadb.utils import embedding_functions
from more_itertools import batched


def get_chroma_client(persist_directory: str) -> chromadb.PersistentClient:
    """Get a ChromaDB client with the specified persistence directory.
    
    Args:
        persist_directory: Directory where ChromaDB will store its data
        
    Returns:
        A ChromaDB PersistentClient
    """
    # Create the directory if it doesn't exist
    os.makedirs(persist_directory, exist_ok=True)
    
    # Return the client
    return chromadb.PersistentClient(persist_directory)


def get_or_create_collection(
    client: chromadb.PersistentClient,
    collection_name: str,
    embedding_model_name: str = "all-MiniLM-L6-v2",
    distance_function: str = "cosine",
) -> chromadb.Collection:
    """Get an existing collection or create a new one if it doesn't exist.
    
    Args:
        client: ChromaDB client
        collection_name: Name of the collection
        embedding_model_name: Name of the embedding model to use
        distance_function: Distance function to use for similarity search
        
    Returns:
        A ChromaDB Collection
    """
    # Create embedding function
    embedding_func = embedding_functions.SentenceTransformerEmbeddingFunction(
        model_name=embedding_model_name
    )
    
    # Try to get the collection, create it if it doesn't exist
    try:
        return client.get_collection(
            name=collection_name,
            embedding_function=embedding_func
        )
    except chromadb.errors.InvalidCollectionException:
        return client.create_collection(
            name=collection_name,
            embedding_function=embedding_func,
            metadata={"hnsw:space": distance_function}
        )


def add_documents_to_collection(
    collection: chromadb.Collection,
    ids: List[str],
    documents: List[str],
    metadatas: Optional[List[Dict[str, Any]]] = None,
    batch_size: int = 100,
) -> None:
    """Add documents to a ChromaDB collection in batches.
    
    Args:
        collection: ChromaDB collection
        ids: List of document IDs
        documents: List of document texts
        metadatas: Optional list of metadata dictionaries for each document
        batch_size: Size of batches for adding documents
    """
    # Create default metadata if none provided
    if metadatas is None:
        metadatas = [{}] * len(documents)
    
    # Create document indices
    document_indices = list(range(len(documents)))
    
    # Add documents in batches
    for batch in batched(document_indices, batch_size):
        # Get the start and end indices for the current batch
        start_idx = batch[0]
        end_idx = batch[-1] + 1  # +1 because end_idx is exclusive
        
        # Add the batch to the collection
        collection.add(
            ids=ids[start_idx:end_idx],
            documents=documents[start_idx:end_idx],
            metadatas=metadatas[start_idx:end_idx],
        )


def query_collection(
    collection: chromadb.Collection,
    query_text: str,
    n_results: int = 5,
    where: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """Query a ChromaDB collection for similar documents.
    
    Args:
        collection: ChromaDB collection
        query_text: Text to search for
        n_results: Number of results to return
        where: Optional filter to apply to the query
        
    Returns:
        Query results containing documents, metadatas, distances, and ids
    """
    # Query the collection
    return collection.query(
        query_texts=[query_text],
        n_results=n_results,
        where=where,
        include=["documents", "metadatas", "distances"]
    )


def format_results_as_context(query_results: Dict[str, Any]) -> str:
    """Format query results as a context string for the agent.
    
    Args:
        query_results: Results from a ChromaDB query
        
    Returns:
        Formatted context string
    """
    context = "CONTEXT INFORMATION:\n\n"
    
    for i, (doc, metadata, distance) in enumerate(zip(
        query_results["documents"][0],
        query_results["metadatas"][0],
        query_results["distances"][0]
    )):
        # Add document information
        context += f"Document {i+1} (Relevance: {1 - distance:.2f}):\n"
        
        # Add metadata if available
        if metadata:
            for key, value in metadata.items():
                context += f"{key}: {value}\n"
        
        # Add document content
        context += f"Content: {doc}\n\n"
    
    return context



================================================
FILE: light-rag-agent/LightRAG/insert_pydantic_docs.py
================================================
import os
import asyncio
from lightrag import LightRAG
from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
from lightrag.kg.shared_storage import initialize_pipeline_status
import dotenv
import httpx

# Load environment variables from .env file
dotenv.load_dotenv()

WORKING_DIR = "./pydantic-docs"

if not os.path.exists(WORKING_DIR):
    os.mkdir(WORKING_DIR)

# URL of the Pydantic AI documentation
PYDANTIC_DOCS_URL = "https://ai.pydantic.dev/llms.txt"

def fetch_pydantic_docs() -> str:
    """Fetch the Pydantic AI documentation from the URL.
    
    Returns:
        The content of the documentation
    """
    try:
        response = httpx.get(PYDANTIC_DOCS_URL)
        response.raise_for_status()
        return response.text
    except Exception as e:
        raise Exception(f"Error fetching Pydantic AI documentation: {e}")


async def initialize_rag():
    rag = LightRAG(
        working_dir=WORKING_DIR,
        embedding_func=openai_embed,
        llm_model_func=gpt_4o_mini_complete
    )

    await rag.initialize_storages()
    await initialize_pipeline_status()

    return rag


def main():
    # Initialize RAG instance and insert Pydantic documentation
    rag = asyncio.run(initialize_rag())
    rag.insert(fetch_pydantic_docs())

if __name__ == "__main__":
    main()


================================================
FILE: light-rag-agent/LightRAG/rag_agent.py
================================================
"""Pydantic AI agent that leverages RAG with a local LightRAG for Pydantic documentation."""

import os
import sys
import argparse
from dataclasses import dataclass
import asyncio

import dotenv
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent
from openai import AsyncOpenAI

from lightrag import LightRAG, QueryParam
from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
from lightrag.kg.shared_storage import initialize_pipeline_status

# Load environment variables from .env file
dotenv.load_dotenv()

WORKING_DIR = "./pydantic-docs"

if not os.path.exists(WORKING_DIR):
    os.mkdir(WORKING_DIR)

# Check for OpenAI API key
if not os.getenv("OPENAI_API_KEY"):
    print("Error: OPENAI_API_KEY environment variable not set.")
    print("Please create a .env file with your OpenAI API key or set it in your environment.")
    sys.exit(1)


async def initialize_rag():
    rag = LightRAG(
        working_dir=WORKING_DIR,
        embedding_func=openai_embed,
        llm_model_func=gpt_4o_mini_complete
    )

    await rag.initialize_storages()

    return rag


@dataclass
class RAGDeps:
    """Dependencies for the RAG agent."""
    lightrag: LightRAG


# Create the Pydantic AI agent
agent = Agent(
    'openai:gpt-4o-mini',
    deps_type=RAGDeps,
    system_prompt="You are a helpful assistant that answers questions about Pydantic AI based on the provided documentation. "
                  "Use the retrieve tool to get relevant information from the Pydantic AI documentation before answering. "
                  "If the documentation doesn't contain the answer, clearly state that the information isn't available "
                  "in the current documentation and provide your best general knowledge response."
)


@agent.tool
async def retrieve(context: RunContext[RAGDeps], search_query: str) -> str:
    """Retrieve relevant documents from ChromaDB based on a search query.
    
    Args:
        context: The run context containing dependencies.
        search_query: The search query to find relevant documents.
        
    Returns:
        Formatted context information from the retrieved documents.
    """
    return await context.deps.lightrag.aquery(
        search_query, param=QueryParam(mode="mix")
    )


async def run_rag_agent(question: str,) -> str:
    """Run the RAG agent to answer a question about Pydantic AI.
    
    Args:
        question: The question to answer.
        
    Returns:
        The agent's response.
    """
    # Create dependencies
    lightrag = await initialize_rag()
    deps = RAGDeps(lightrag=lightrag)
    
    # Run the agent
    result = await agent.run(question, deps=deps)
    
    return result.data


def main():
    """Main function to parse arguments and run the RAG agent."""
    parser = argparse.ArgumentParser(description="Run a Pydantic AI agent with RAG using ChromaDB")
    parser.add_argument("--question", help="The question to answer about Pydantic AI")
    
    args = parser.parse_args()
    
    # Run the agent
    response = asyncio.run(run_rag_agent(args.question))
    
    print("\nResponse:")
    print(response)


if __name__ == "__main__":
    main()



================================================
FILE: light-rag-agent/LightRAG/requirements.txt
================================================
[Binary file]


================================================
FILE: light-rag-agent/LightRAG/streamlit_app.py
================================================
from dotenv import load_dotenv
import streamlit as st
import asyncio
import os

# Import all the message part classes
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    UserPromptPart,
    TextPart,
    ToolCallPart,
    ToolReturnPart,
    RetryPromptPart,
    ModelMessagesTypeAdapter
)

from lightrag import LightRAG
from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
from lightrag.kg.shared_storage import initialize_pipeline_status

from rag_agent import agent, RAGDeps

load_dotenv()

async def get_agent_deps():
    """
    Creates a LightRAG instance
    And then uses that to create the Pydantic AI agent dependencies.
    """
    WORKING_DIR = "./pydantic-docs"

    if not os.path.exists(WORKING_DIR):
        os.mkdir(WORKING_DIR)

    rag = LightRAG(
        working_dir=WORKING_DIR,
        embedding_func=openai_embed,
        llm_model_func=gpt_4o_mini_complete
    )

    await rag.initialize_storages()
    deps = RAGDeps(lightrag=rag)
    return deps


def display_message_part(part):
    """
    Display a single part of a message in the Streamlit UI.
    Customize how you display system prompts, user prompts,
    tool calls, tool returns, etc.
    """
    # user-prompt
    if part.part_kind == 'user-prompt':
        with st.chat_message("user"):
            st.markdown(part.content)
    # text
    elif part.part_kind == 'text':
        with st.chat_message("assistant"):
            st.markdown(part.content)             

async def run_agent_with_streaming(user_input):
    async with agent.run_stream(
        user_input, deps=st.session_state.agent_deps, message_history=st.session_state.messages
    ) as result:
        async for message in result.stream_text(delta=True):  
            yield message

    # Add the new messages to the chat history (including tool calls and responses)
    st.session_state.messages.extend(result.new_messages())


# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~ Main Function with UI Creation ~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

async def main():
    st.title("LightRAG AI Agent")

    # Initialize chat history in session state if not present
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "agent_deps" not in st.session_state:
        st.session_state.agent_deps = await get_agent_deps()  

    # Display all messages from the conversation so far
    # Each message is either a ModelRequest or ModelResponse.
    # We iterate over their parts to decide how to display them.
    for msg in st.session_state.messages:
        if isinstance(msg, ModelRequest) or isinstance(msg, ModelResponse):
            for part in msg.parts:
                display_message_part(part)

    # Chat input for the user
    user_input = st.chat_input("What do you want to know?")

    if user_input:
        # Display user prompt in the UI
        with st.chat_message("user"):
            st.markdown(user_input)

        # Display the assistant's partial response while streaming
        with st.chat_message("assistant"):
            # Create a placeholder for the streaming text
            message_placeholder = st.empty()
            full_response = ""
            
            # Properly consume the async generator with async for
            generator = run_agent_with_streaming(user_input)
            async for message in generator:
                full_response += message
                message_placeholder.markdown(full_response + "▌")
            
            # Final response without the cursor
            message_placeholder.markdown(full_response)


if __name__ == "__main__":
    asyncio.run(main())



================================================
FILE: light-rag-agent/LightRAG/super-basic-lightrag.py
================================================
import asyncio
from lightrag import LightRAG, QueryParam
from lightrag.llm.openai import gpt_4o_mini_complete, gpt_4o_complete, openai_embed
from lightrag.kg.shared_storage import initialize_pipeline_status

async def main():
    # Initialize RAG instance
    rag = LightRAG(
        working_dir="data/",
        embedding_func=openai_embed,
        llm_model_func=gpt_4o_mini_complete
    )

    await rag.initialize_storages()
    await initialize_pipeline_status()

    # Insert text
    await rag.ainsert("The most popular AI agent framework of all time is probably Langchain.")
    await rag.ainsert("Under the Langchain hood we also have LangGraph, LangServe, and LangSmith.")
    await rag.ainsert("Many people prefer using other frameworks like Agno or Pydantic AI instead of Langchain.")
    await rag.ainsert("It is very easy to use Python with all of these AI agent frameworks.")

    # Run the query
    result = await rag.aquery(
        "What programming language should I use for coding AI agents?",
        param=QueryParam(mode="mix")
    )

    print(result)

if __name__ == "__main__":
    asyncio.run(main())


================================================
FILE: light-rag-agent/LightRAG/.env.example
================================================
# Get your API key here: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
OPENAI_API_EKY=

